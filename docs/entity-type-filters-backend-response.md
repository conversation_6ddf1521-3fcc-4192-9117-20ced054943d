# Entity Type Filters Backend Response

## Problem Identified ✅

The issue is a **key naming mismatch** between frontend and backend expectations.

### Frontend is sending:
```json
{
  "AI Tool": {
    "technical_levels": ["BEGINNER"],
    "pricing_models": ["FREE"]
  }
}
```

### Backend expects:
```json
{
  "tool": {
    "technical_levels": ["BEGINNER"],
    "pricing_models": ["FREE"]
  }
}
```

## Root Cause

The backend DTO uses **snake_case entity type slugs** as keys, not the display names.

## Expected Structure (Option B: Snake case keys) ✅

The correct format is **Option B** from your document:

```json
{
  "tool": { "technical_levels": ["BEGINNER"] },
  "course": { "skill_levels": ["INTERMEDIATE"] },
  "job": { "employment_types": ["Full-time"] }
}
```

## Complete Entity Type Mapping

| Frontend Display Name | Backend Key | Available Filters |
|----------------------|-------------|-------------------|
| "AI Tool" | `tool` | technical_levels, learning_curves, pricing_models, etc. |
| "Course" | `course` | skill_levels, certificate_available, instructor_name, etc. |
| "Job" | `job` | employment_types, experience_levels, salary_min, etc. |
| "Hardware" | `hardware` | hardware_types, manufacturers, price_min, etc. |
| "Event" | `event` | event_types, start_date_from, location, etc. |
| "Agency" | `agency` | services_offered, industry_focus, etc. |
| "Software" | `software` | current_version, license_type, etc. |
| "Research Paper" | `research_paper` | authors, publication_date, etc. |
| "Podcast" | `podcast` | frequency, host, main_topics, etc. |
| "Community" | `community` | platform, member_count, etc. |
| "Grant" | `grant` | funding_organizations, research_areas, etc. |
| "Newsletter" | `newsletter` | frequency, focus_areas, etc. |
| "Book" | `book` | author, publication_date, etc. |

## DTO Definition

```typescript
export class EntityTypeFiltersDto {
  tool?: ToolFiltersDto;
  course?: CourseFiltersDto;
  job?: JobFiltersDto;
  hardware?: HardwareFiltersDto;
  event?: EventFiltersDto;
  agency?: AgencyFiltersDto;
  software?: SoftwareFiltersDto;
  research_paper?: ResearchPaperFiltersDto;
  podcast?: PodcastFiltersDto;
  community?: CommunityFiltersDto;
  grant?: GrantFiltersDto;
  newsletter?: NewsletterFiltersDto;
  book?: BookFiltersDto;
}
```

## Frontend Fix Required

### 1. Create Entity Type Mapping Function

```typescript
const ENTITY_TYPE_MAPPING = {
  'AI Tool': 'tool',
  'Course': 'course',
  'Job': 'job',
  'Hardware': 'hardware',
  'Event': 'event',
  'Agency': 'agency',
  'Software': 'software',
  'Research Paper': 'research_paper',
  'Podcast': 'podcast',
  'Community': 'community',
  'Grant': 'grant',
  'Newsletter': 'newsletter',
  'Book': 'book',
  'Platform': 'software', // Assuming Platform maps to Software
  'API': 'tool', // Assuming API maps to Tool
};

function convertEntityTypeFilters(frontendFilters: Record<string, any>) {
  const backendFilters: Record<string, any> = {};
  
  for (const [displayName, filters] of Object.entries(frontendFilters)) {
    const backendKey = ENTITY_TYPE_MAPPING[displayName];
    if (backendKey && filters) {
      backendFilters[backendKey] = filters;
    }
  }
  
  return backendFilters;
}
```

### 2. Update API Call

```typescript
// Before sending to backend
const frontendFilters = {
  "AI Tool": {
    "technical_levels": ["BEGINNER"],
    "pricing_models": ["FREE"]
  }
};

// Convert for backend
const backendFilters = convertEntityTypeFilters(frontendFilters);
// Result: { "tool": { "technical_levels": ["BEGINNER"], "pricing_models": ["FREE"] } }

// Send to API
const params = new URLSearchParams();
params.append('entity_type_filters', JSON.stringify(backendFilters));
```

## Working Examples

### Tool Filters
```bash
curl -X GET "http://localhost:3000/entities?entity_type_filters=%7B%22tool%22%3A%7B%22technical_levels%22%3A%5B%22BEGINNER%22%5D%7D%7D"
```

### Course Filters  
```bash
curl -X GET "http://localhost:3000/entities?entity_type_filters=%7B%22course%22%3A%7B%22skill_levels%22%3A%5B%22INTERMEDIATE%22%5D%7D%7D"
```

### Job Filters
```bash
curl -X GET "http://localhost:3000/entities?entity_type_filters=%7B%22job%22%3A%7B%22employment_types%22%3A%5B%22Full-time%22%5D%7D%7D"
```

## Validation Rules

- Keys must be exact snake_case slugs (e.g., `tool`, not `Tool` or `AI Tool`)
- Values must match the specific DTO structure for each entity type
- Unknown keys will be rejected by validation
- All filters are optional within each entity type

## Next Steps for Frontend

1. ✅ **Implement mapping function** above
2. ✅ **Update all entity type filter calls** to use backend keys
3. ✅ **Test with each entity type** to ensure filters work
4. ✅ **Update documentation** with correct format

## Detailed Filter Specifications

### Tool Filters (key: "tool")
```typescript
{
  technical_levels?: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'][];
  learning_curves?: ['EASY', 'MODERATE', 'STEEP'][];
  pricing_models?: ['FREE', 'FREEMIUM', 'SUBSCRIPTION', 'ONE_TIME'][];
  price_ranges?: ['FREE', 'UNDER_10', 'UNDER_50', 'UNDER_100', 'OVER_100'][];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  platforms?: string[];
  integrations?: string[];
  frameworks?: string[];
  libraries?: string[];
  key_features_search?: string;
  use_cases_search?: string;
  target_audience_search?: string;
  deployment_options?: string[];
  support_channels?: string[];
  has_live_chat?: boolean;
  customization_level?: string;
  pricing_details_search?: string;
}
```

### Course Filters (key: "course")
```typescript
{
  skill_levels?: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'][];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;
  has_syllabus?: boolean;
}
```

### Job Filters (key: "job")
```typescript
{
  employment_types?: string[];
  experience_levels?: string[];
  location_types?: string[];
  company_name?: string;
  job_title?: string;
  salary_min?: number; // in thousands
  salary_max?: number; // in thousands
  job_description?: string;
  has_application_url?: boolean;
}
```

## Testing Examples

### Valid Request (Tool with BEGINNER level)
```bash
# URL encoded: {"tool":{"technical_levels":["BEGINNER"]}}
GET /entities?entity_type_filters=%7B%22tool%22%3A%7B%22technical_levels%22%3A%5B%22BEGINNER%22%5D%7D%7D
```

### Invalid Request (using display name)
```bash
# This will fail with validation error
GET /entities?entity_type_filters=%7B%22AI%20Tool%22%3A%7B%22technical_levels%22%3A%5B%22BEGINNER%22%5D%7D%7D
```

## Backend Status

✅ **No backend changes needed** - the validation is working correctly and rejecting invalid keys as expected.

The error `entity_type_filters.property AI Tool should not exist` is the validation system correctly rejecting the unknown key `"AI Tool"` since only predefined keys like `"tool"` are allowed.

## Implementation Priority

1. **High Priority**: Fix the key mapping for Tool, Course, Job, Hardware, Event
2. **Medium Priority**: Add remaining entity types (Agency, Software, etc.)
3. **Low Priority**: Add comprehensive filter validation on frontend

## Contact & Support

- Backend validation is working correctly ✅
- Frontend needs to implement the key mapping ✅
- All filter parameters are properly validated ✅
- No backend changes required ✅
