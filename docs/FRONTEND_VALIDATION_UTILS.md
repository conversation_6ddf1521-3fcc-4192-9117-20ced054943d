# Frontend Validation & Testing Utilities

## 🛡️ Client-Side Validation

### Filter Validation Utilities
```typescript
// utils/filterValidation.ts
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class FilterValidator {
  static validateEntityFilters(filters: EntityFilters): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate salary range
    if (filters.salary_min && filters.salary_max && filters.salary_min > filters.salary_max) {
      errors.push('Minimum salary cannot be greater than maximum salary');
    }
    
    // Validate date ranges
    if (filters.start_date_from && filters.start_date_to) {
      const fromDate = new Date(filters.start_date_from);
      const toDate = new Date(filters.start_date_to);
      if (fromDate > toDate) {
        errors.push('Start date "from" cannot be after "to" date');
      }
    }
    
    if (filters.publication_date_from && filters.publication_date_to) {
      const fromDate = new Date(filters.publication_date_from);
      const toDate = new Date(filters.publication_date_to);
      if (fromDate > toDate) {
        errors.push('Publication date "from" cannot be after "to" date');
      }
    }
    
    // Validate enrollment range
    if (filters.enrollment_min && filters.enrollment_max && filters.enrollment_min > filters.enrollment_max) {
      errors.push('Minimum enrollment cannot be greater than maximum enrollment');
    }
    
    // Validate price range
    if (filters.price_min && filters.price_max && filters.price_min > filters.price_max) {
      errors.push('Minimum price cannot be greater than maximum price');
    }
    
    // Validate page and limit
    if (filters.page && filters.page < 1) {
      errors.push('Page number must be greater than 0');
    }
    
    if (filters.limit && (filters.limit < 1 || filters.limit > 100)) {
      errors.push('Limit must be between 1 and 100');
    }
    
    // Warnings for potentially inefficient queries
    const activeFilters = Object.entries(filters).filter(([key, value]) => {
      if (key === 'page' || key === 'limit') return false;
      if (Array.isArray(value)) return value.length > 0;
      return value !== undefined && value !== null && value !== '';
    });
    
    if (activeFilters.length === 0) {
      warnings.push('No filters applied - this may return a large number of results');
    }
    
    if (activeFilters.length > 10) {
      warnings.push('Many filters applied - consider using search instead for better performance');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
  
  static validateSearchQuery(query: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (query.length < 2) {
      errors.push('Search query must be at least 2 characters long');
    }
    
    if (query.length > 100) {
      errors.push('Search query cannot exceed 100 characters');
    }
    
    // Check for potentially problematic characters
    const problematicChars = /[<>{}[\]\\]/;
    if (problematicChars.test(query)) {
      warnings.push('Search query contains special characters that may affect results');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
```

### Form Validation Hook
```typescript
// hooks/useFormValidation.ts
import { useState, useCallback } from 'react';
import { FilterValidator, ValidationResult } from '../utils/filterValidation';
import { EntityFilters } from '../api/entities';

export const useFormValidation = () => {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });
  
  const validateFilters = useCallback((filters: EntityFilters) => {
    const result = FilterValidator.validateEntityFilters(filters);
    setValidationResult(result);
    return result;
  }, []);
  
  const validateSearch = useCallback((query: string) => {
    const result = FilterValidator.validateSearchQuery(query);
    setValidationResult(result);
    return result;
  }, []);
  
  const clearValidation = useCallback(() => {
    setValidationResult({
      isValid: true,
      errors: [],
      warnings: []
    });
  }, []);
  
  return {
    validationResult,
    validateFilters,
    validateSearch,
    clearValidation
  };
};
```

## 🧪 Testing Framework

### Mock Data Generator
```typescript
// utils/mockData.ts
export const generateMockEntityResponse = (filters: EntityFilters): EntityResponse => {
  const mockEntities = [
    {
      id: '1',
      name: 'ChatGPT',
      description: 'AI-powered conversational assistant',
      entityType: { name: 'Tool' },
      avgRating: 4.5,
      reviewCount: 1250,
      createdAt: '2023-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'Machine Learning Course',
      description: 'Comprehensive ML course for beginners',
      entityType: { name: 'Course' },
      avgRating: 4.8,
      reviewCount: 890,
      createdAt: '2023-02-01T00:00:00Z'
    },
    {
      id: '3',
      name: 'AI Engineer Position',
      description: 'Senior AI Engineer role at tech startup',
      entityType: { name: 'Job' },
      avgRating: 4.2,
      reviewCount: 45,
      createdAt: '2023-03-01T00:00:00Z'
    }
  ];
  
  // Filter mock data based on applied filters
  let filteredEntities = mockEntities;
  
  if (filters.search) {
    filteredEntities = filteredEntities.filter(entity =>
      entity.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
      entity.description.toLowerCase().includes(filters.search!.toLowerCase())
    );
  }
  
  if (filters.entity_types?.length) {
    filteredEntities = filteredEntities.filter(entity =>
      filters.entity_types!.includes(entity.entityType.name)
    );
  }
  
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    data: filteredEntities.slice(startIndex, endIndex),
    total: filteredEntities.length,
    page,
    limit,
    totalPages: Math.ceil(filteredEntities.length / limit)
  };
};
```

### Component Testing Utilities
```typescript
// utils/testUtils.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { EntitiesAPI } from '../api/entities';
import { generateMockEntityResponse } from './mockData';

// Mock the API
jest.mock('../api/entities');
const mockEntitiesAPI = EntitiesAPI as jest.Mocked<typeof EntitiesAPI>;

export const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

export const setupMockAPI = () => {
  mockEntitiesAPI.getEntities.mockImplementation(async (filters) => {
    return generateMockEntityResponse(filters);
  });
};

export const testFilterComponent = async (
  component: React.ReactElement,
  filterTests: Array<{
    name: string;
    action: () => void;
    expectedFilters: Partial<EntityFilters>;
  }>
) => {
  setupMockAPI();
  renderWithRouter(component);
  
  for (const test of filterTests) {
    console.log(`Testing: ${test.name}`);
    
    // Perform the action
    test.action();
    
    // Wait for API call
    await waitFor(() => {
      expect(mockEntitiesAPI.getEntities).toHaveBeenCalledWith(
        expect.objectContaining(test.expectedFilters)
      );
    });
  }
};
```

### Example Component Tests
```typescript
// __tests__/ToolFilters.test.tsx
import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import { ToolFilters } from '../components/ToolFilters';
import { testFilterComponent } from '../utils/testUtils';

describe('ToolFilters', () => {
  it('should apply tool-specific filters correctly', async () => {
    await testFilterComponent(<ToolFilters />, [
      {
        name: 'API Access Filter',
        action: () => {
          const checkbox = screen.getByLabelText('Has API Access');
          fireEvent.click(checkbox);
        },
        expectedFilters: { has_api: true }
      },
      {
        name: 'Technical Level Filter',
        action: () => {
          const select = screen.getByLabelText('Technical Levels:');
          fireEvent.change(select, { target: { value: ['BEGINNER'] } });
        },
        expectedFilters: { technical_levels: ['BEGINNER'] }
      },
      {
        name: 'Multiple Filters',
        action: () => {
          const apiCheckbox = screen.getByLabelText('Has API Access');
          const freeTierCheckbox = screen.getByLabelText('Has Free Tier');
          fireEvent.click(apiCheckbox);
          fireEvent.click(freeTierCheckbox);
        },
        expectedFilters: { has_api: true, has_free_tier: true }
      }
    ]);
  });
});
```

## 🔍 Performance Monitoring

### API Performance Tracker
```typescript
// utils/performanceTracker.ts
export class APIPerformanceTracker {
  private static metrics: Map<string, number[]> = new Map();
  
  static trackRequest(endpoint: string, duration: number) {
    if (!this.metrics.has(endpoint)) {
      this.metrics.set(endpoint, []);
    }
    this.metrics.get(endpoint)!.push(duration);
  }
  
  static getAverageResponseTime(endpoint: string): number {
    const times = this.metrics.get(endpoint) || [];
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  
  static getMetrics() {
    const result: Record<string, { average: number; count: number; max: number; min: number }> = {};
    
    this.metrics.forEach((times, endpoint) => {
      if (times.length > 0) {
        result[endpoint] = {
          average: this.getAverageResponseTime(endpoint),
          count: times.length,
          max: Math.max(...times),
          min: Math.min(...times)
        };
      }
    });
    
    return result;
  }
  
  static reset() {
    this.metrics.clear();
  }
}

// Enhanced API service with performance tracking
export class TrackedEntitiesAPI extends EntitiesAPI {
  static async getEntities(filters: EntityFilters = {}): Promise<EntityResponse> {
    const startTime = performance.now();
    
    try {
      const result = await super.getEntities(filters);
      const duration = performance.now() - startTime;
      
      APIPerformanceTracker.trackRequest('/entities', duration);
      
      // Log slow requests
      if (duration > 2000) {
        console.warn(`Slow API request detected: ${duration.toFixed(2)}ms`, { filters });
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      APIPerformanceTracker.trackRequest('/entities', duration);
      throw error;
    }
  }
}
```

## 🎯 Integration Checklist

### Pre-Deployment Checklist
```typescript
// utils/integrationChecklist.ts
export interface ChecklistItem {
  name: string;
  description: string;
  check: () => Promise<boolean>;
  required: boolean;
}

export const integrationChecklist: ChecklistItem[] = [
  {
    name: 'API Connectivity',
    description: 'Verify API endpoint is accessible',
    required: true,
    check: async () => {
      try {
        const response = await fetch('/entities?limit=1');
        return response.ok;
      } catch {
        return false;
      }
    }
  },
  {
    name: 'Tool Filters',
    description: 'Test tool-specific filtering',
    required: true,
    check: async () => {
      try {
        const response = await EntitiesAPI.getEntities({ has_api: true, limit: 1 });
        return response.total >= 0;
      } catch {
        return false;
      }
    }
  },
  {
    name: 'Hardware Filters',
    description: 'Test enhanced hardware filtering',
    required: true,
    check: async () => {
      try {
        const response = await EntitiesAPI.getEntities({ hardware_types: ['GPU'], limit: 1 });
        return response.total >= 0;
      } catch {
        return false;
      }
    }
  },
  {
    name: 'Cross-Entity Filtering',
    description: 'Test filtering across multiple entity types',
    required: true,
    check: async () => {
      try {
        const response = await EntitiesAPI.getEntities({ 
          has_api: true, 
          certificate_available: true,
          limit: 1 
        });
        return response.total >= 0;
      } catch {
        return false;
      }
    }
  },
  {
    name: 'URL State Management',
    description: 'Verify URL parameters are properly handled',
    required: true,
    check: async () => {
      const params = new URLSearchParams('has_api=true&technical_levels=BEGINNER');
      return params.get('has_api') === 'true' && params.getAll('technical_levels').includes('BEGINNER');
    }
  }
];

export const runIntegrationChecklist = async (): Promise<{ passed: number; failed: number; results: Array<{ item: ChecklistItem; passed: boolean; error?: string }> }> => {
  const results = [];
  let passed = 0;
  let failed = 0;
  
  for (const item of integrationChecklist) {
    try {
      const result = await item.check();
      results.push({ item, passed: result });
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      results.push({ item, passed: false, error: error.message });
      failed++;
    }
  }
  
  return { passed, failed, results };
};
```

## ✅ Ready for Production

Your frontend integration is complete with:
- ✅ Comprehensive validation utilities
- ✅ Testing framework with mock data
- ✅ Performance monitoring
- ✅ Integration checklist
- ✅ All entity-specific filters working

Run the integration checklist before deploying to ensure everything works correctly!
