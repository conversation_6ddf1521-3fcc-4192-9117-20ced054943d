/**
 * Entity Type Filters Utility for Frontend
 * 
 * This utility converts frontend entity type display names to backend-expected keys
 * and provides type definitions for all available filters.
 */

// Entity Type Mapping: Frontend Display Name -> Backend Key
export const ENTITY_TYPE_MAPPING: Record<string, string> = {
  'AI Tool': 'tool',
  'Course': 'course',
  'Job': 'job',
  'Hardware': 'hardware',
  'Event': 'event',
  'Agency': 'agency',
  'Software': 'software',
  'Research Paper': 'research_paper',
  'Podcast': 'podcast',
  'Community': 'community',
  'Grant': 'grant',
  'Newsletter': 'newsletter',
  'Book': 'book',
  'Platform': 'software', // Platform maps to Software
  'API': 'tool', // API maps to Tool
  'Model': 'tool', // Model maps to Tool (if needed)
  'Dataset': 'tool', // Dataset maps to Tool (if needed)
};

// Reverse mapping for display purposes
export const BACKEND_TO_DISPLAY_MAPPING: Record<string, string> = Object.fromEntries(
  Object.entries(ENTITY_TYPE_MAPPING).map(([display, backend]) => [backend, display])
);

// Type definitions for each entity type filter
export interface ToolFilters {
  technical_levels?: ('BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT')[];
  learning_curves?: ('EASY' | 'MODERATE' | 'STEEP')[];
  pricing_models?: ('FREE' | 'FREEMIUM' | 'SUBSCRIPTION' | 'ONE_TIME')[];
  price_ranges?: ('FREE' | 'UNDER_10' | 'UNDER_50' | 'UNDER_100' | 'OVER_100')[];
  has_api?: boolean;
  has_free_tier?: boolean;
  open_source?: boolean;
  mobile_support?: boolean;
  demo_available?: boolean;
  platforms?: string[];
  integrations?: string[];
  frameworks?: string[];
  libraries?: string[];
  key_features_search?: string;
  use_cases_search?: string;
  target_audience_search?: string;
  deployment_options?: string[];
  support_channels?: string[];
  has_live_chat?: boolean;
  customization_level?: string;
  pricing_details_search?: string;
}

export interface CourseFilters {
  skill_levels?: ('BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT')[];
  certificate_available?: boolean;
  instructor_name?: string;
  duration_text?: string;
  enrollment_min?: number;
  enrollment_max?: number;
  prerequisites?: string;
  has_syllabus?: boolean;
}

export interface JobFilters {
  employment_types?: string[];
  experience_levels?: string[];
  location_types?: string[];
  company_name?: string;
  job_title?: string;
  salary_min?: number; // in thousands
  salary_max?: number; // in thousands
  job_description?: string;
  has_application_url?: boolean;
}

export interface HardwareFilters {
  hardware_types?: string[];
  manufacturers?: string[];
  release_date_from?: string;
  release_date_to?: string;
  price_min?: number;
  price_max?: number;
  specifications_search?: string;
  has_datasheet?: boolean;
  memory_search?: string;
  processor_search?: string;
}

export interface EventFilters {
  event_types?: string[];
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  is_online?: boolean;
  location?: string;
  price_text?: string;
  registration_required?: boolean;
  has_registration_url?: boolean;
  speakers_search?: string;
  target_audience_search?: string;
}

// Union type for all possible filters
export type EntityTypeFilters = {
  tool?: ToolFilters;
  course?: CourseFilters;
  job?: JobFilters;
  hardware?: HardwareFilters;
  event?: EventFilters;
  agency?: Record<string, any>; // Add specific type if needed
  software?: Record<string, any>; // Add specific type if needed
  research_paper?: Record<string, any>; // Add specific type if needed
  podcast?: Record<string, any>; // Add specific type if needed
  community?: Record<string, any>; // Add specific type if needed
  grant?: Record<string, any>; // Add specific type if needed
  newsletter?: Record<string, any>; // Add specific type if needed
  book?: Record<string, any>; // Add specific type if needed
};

/**
 * Converts frontend entity type filters to backend format
 * 
 * @param frontendFilters - Object with display names as keys
 * @returns Object with backend keys
 */
export function convertEntityTypeFilters(
  frontendFilters: Record<string, any>
): EntityTypeFilters {
  const backendFilters: EntityTypeFilters = {};
  
  for (const [displayName, filters] of Object.entries(frontendFilters)) {
    const backendKey = ENTITY_TYPE_MAPPING[displayName];
    if (backendKey && filters && Object.keys(filters).length > 0) {
      (backendFilters as any)[backendKey] = filters;
    }
  }
  
  return backendFilters;
}

/**
 * Validates that entity type filters have the correct structure
 * 
 * @param filters - Filters to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateEntityTypeFilters(filters: Record<string, any>): string[] {
  const errors: string[] = [];
  
  for (const [displayName, filterObj] of Object.entries(filters)) {
    if (!ENTITY_TYPE_MAPPING[displayName]) {
      errors.push(`Unknown entity type: "${displayName}". Valid types: ${Object.keys(ENTITY_TYPE_MAPPING).join(', ')}`);
      continue;
    }
    
    if (typeof filterObj !== 'object' || filterObj === null) {
      errors.push(`Filters for "${displayName}" must be an object`);
      continue;
    }
    
    // Add specific validation for each entity type if needed
    if (displayName === 'AI Tool' && filterObj.technical_levels) {
      const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
      const invalidLevels = filterObj.technical_levels.filter((level: string) => !validLevels.includes(level));
      if (invalidLevels.length > 0) {
        errors.push(`Invalid technical_levels for AI Tool: ${invalidLevels.join(', ')}. Valid: ${validLevels.join(', ')}`);
      }
    }
  }
  
  return errors;
}

/**
 * Example usage:
 * 
 * const frontendFilters = {
 *   "AI Tool": {
 *     "technical_levels": ["BEGINNER"],
 *     "pricing_models": ["FREE"]
 *   },
 *   "Course": {
 *     "skill_levels": ["INTERMEDIATE"],
 *     "certificate_available": true
 *   }
 * };
 * 
 * // Validate
 * const errors = validateEntityTypeFilters(frontendFilters);
 * if (errors.length > 0) {
 *   console.error('Validation errors:', errors);
 *   return;
 * }
 * 
 * // Convert for backend
 * const backendFilters = convertEntityTypeFilters(frontendFilters);
 * // Result: { "tool": { "technical_levels": ["BEGINNER"], "pricing_models": ["FREE"] }, "course": { ... } }
 * 
 * // Use in API call
 * const params = new URLSearchParams();
 * params.append('entity_type_filters', JSON.stringify(backendFilters));
 * 
 * const response = await fetch(`/entities?${params.toString()}`);
 */

export default {
  ENTITY_TYPE_MAPPING,
  BACKEND_TO_DISPLAY_MAPPING,
  convertEntityTypeFilters,
  validateEntityTypeFilters,
};
