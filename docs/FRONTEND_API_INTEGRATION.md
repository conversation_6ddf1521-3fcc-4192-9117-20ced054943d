# Frontend API Integration Guide: Entity Filtering System

## 🎉 What's New

The backend has been completely refactored to support **comprehensive entity-specific filtering** using a clean, flat parameter API design. All the entity-specific filters your frontend team implemented are now **fully functional** on the backend.

## 🔄 Migration Required

### Before (Nested JSON - DEPRECATED)
```javascript
// ❌ OLD APPROACH - No longer supported
const filters = {
  entity_type_filters: {
    "tool": { "has_api": true, "technical_levels": ["BEGINNER"] },
    "course": { "skill_levels": ["INTERMEDIATE"], "certificate_available": true }
  }
};

const response = await fetch(`/entities?entity_type_filters=${JSON.stringify(filters)}`);
```

### After (Flat Parameters - NEW)
```javascript
// ✅ NEW APPROACH - Use this now
const params = new URLSearchParams();
params.append('has_api', 'true');
params.append('technical_levels', 'BEGINNER');
params.append('skill_levels', 'INTERMEDIATE');
params.append('certificate_available', 'true');

const response = await fetch(`/entities?${params.toString()}`);
```

## 📋 Complete Parameter Reference

### Array Parameters
For array parameters, append multiple values or use comma-separated strings:

```javascript
// Method 1: Multiple append calls (recommended)
params.append('technical_levels', 'BEGINNER');
params.append('technical_levels', 'INTERMEDIATE');

// Method 2: Comma-separated (also supported)
params.append('technical_levels', 'BEGINNER,INTERMEDIATE');

// Result: ?technical_levels=BEGINNER&technical_levels=INTERMEDIATE
```

### Tool/AI Tool Filters
```javascript
// Technical & Learning
technical_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
learning_curves: ['LOW', 'MEDIUM', 'HIGH']

// Features & Access
has_api: boolean
has_free_tier: boolean
open_source: boolean
mobile_support: boolean
demo_available: boolean
has_live_chat: boolean

// Integration & Tech
frameworks: string[]           // e.g., ['React', 'Vue', 'Angular']
libraries: string[]            // e.g., ['TensorFlow', 'PyTorch']
deployment_options: string[]   // e.g., ['Cloud', 'On-premise']
support_channels: string[]     // e.g., ['Email', 'Chat', 'Phone']

// Search Fields
key_features_search: string
use_cases_search: string
target_audience_search: string
customization_level: string
pricing_details_search: string
```

### Course Filters
```javascript
skill_levels: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
certificate_available: boolean
instructor_name: string
duration_text: string
enrollment_min: number
enrollment_max: number
prerequisites: string
has_syllabus: boolean
```

### Job Filters
```javascript
employment_types: string[]     // e.g., ['FULL_TIME', 'PART_TIME', 'CONTRACT']
experience_levels: string[]    // e.g., ['ENTRY', 'MID', 'SENIOR']
location_types: string[]       // e.g., ['Remote', 'Hybrid', 'On-site']
company_name: string
job_title: string
salary_min: number            // in thousands
salary_max: number            // in thousands
has_application_url: boolean
```

### Hardware Filters
```javascript
// Note: Limited fields available in current schema
price_range: string
price_min: number
price_max: number
memory_search: string         // searches in memory field
processor_search: string      // searches in processor field
```

### Event Filters
```javascript
event_types: string[]         // e.g., ['Conference', 'Workshop', 'Webinar']
start_date_from: string       // YYYY-MM-DD format
start_date_to: string         // YYYY-MM-DD format
end_date_from: string         // YYYY-MM-DD format
end_date_to: string           // YYYY-MM-DD format
is_online: boolean
location: string
price_text: string
has_registration_url: boolean
speakers_search: string       // searches in keySpeakers array
```

### Agency Filters
```javascript
services_offered: string[]    // e.g., ['SEO', 'PPC', 'Content Marketing']
industry_focus: string[]      // e.g., ['Healthcare', 'Finance', 'E-commerce']
has_portfolio: boolean
```

### Software Filters
```javascript
license_types: string[]       // e.g., ['MIT', 'GPL', 'Apache']
current_version: string
// Note: Some fields like programming_languages, platform_compatibility not yet in schema
```

### Book Filters
```javascript
author_name: string
isbn: string
formats: string[]            // e.g., ['PDF', 'Hardcover', 'Ebook']
```

## 🛠️ Implementation Examples

### Basic Filtering
```javascript
async function fetchEntitiesWithFilters(filters) {
  const params = new URLSearchParams();
  
  // Add basic pagination
  params.append('page', filters.page || '1');
  params.append('limit', filters.limit || '20');
  
  // Add search term
  if (filters.search) {
    params.append('search', filters.search);
  }
  
  // Add entity type filter
  if (filters.entityTypes?.length) {
    filters.entityTypes.forEach(type => params.append('entity_types', type));
  }
  
  // Add tool-specific filters
  if (filters.hasApi) params.append('has_api', 'true');
  if (filters.hasFreeTier) params.append('has_free_tier', 'true');
  if (filters.technicalLevels?.length) {
    filters.technicalLevels.forEach(level => params.append('technical_levels', level));
  }
  
  const response = await fetch(`/entities?${params.toString()}`);
  return response.json();
}
```

### Advanced Multi-Entity Filtering
```javascript
async function fetchMixedEntityFilters() {
  const params = new URLSearchParams();
  
  // Tools with API access
  params.append('has_api', 'true');
  params.append('technical_levels', 'BEGINNER');
  
  // Courses with certificates
  params.append('certificate_available', 'true');
  params.append('skill_levels', 'INTERMEDIATE');
  
  // Remote jobs
  params.append('location_types', 'Remote');
  params.append('employment_types', 'FULL_TIME');
  
  // Online events
  params.append('is_online', 'true');
  
  const response = await fetch(`/entities?${params.toString()}`);
  return response.json();
}
```

### Form Integration
```javascript
function buildFiltersFromForm(formData) {
  const params = new URLSearchParams();
  
  // Handle checkboxes
  if (formData.hasApi) params.append('has_api', 'true');
  if (formData.hasFreeTier) params.append('has_free_tier', 'true');
  if (formData.openSource) params.append('open_source', 'true');
  
  // Handle multi-select dropdowns
  if (formData.technicalLevels) {
    formData.technicalLevels.forEach(level => {
      params.append('technical_levels', level);
    });
  }
  
  // Handle text inputs
  if (formData.companyName) params.append('company_name', formData.companyName);
  if (formData.instructorName) params.append('instructor_name', formData.instructorName);
  
  // Handle number ranges
  if (formData.salaryMin) params.append('salary_min', formData.salaryMin.toString());
  if (formData.salaryMax) params.append('salary_max', formData.salaryMax.toString());
  
  return params.toString();
}
```

## 🎯 UI/UX Recommendations

### Remove "Coming Soon" Notices
All entity-specific filters are now functional. Remove any "Coming Soon" or disabled states from:
- Tool filtering forms
- Course filtering forms  
- Job filtering forms
- Event filtering forms
- Hardware filtering forms
- Agency filtering forms
- Software filtering forms
- Book filtering forms

### URL State Management
```javascript
// Update URL with current filters for bookmarking/sharing
function updateUrlWithFilters(filters) {
  const params = buildFiltersFromForm(filters);
  const newUrl = `${window.location.pathname}?${params}`;
  window.history.pushState({}, '', newUrl);
}

// Parse URL parameters on page load
function parseFiltersFromUrl() {
  const params = new URLSearchParams(window.location.search);
  return {
    hasApi: params.get('has_api') === 'true',
    technicalLevels: params.getAll('technical_levels'),
    certificateAvailable: params.get('certificate_available') === 'true',
    // ... etc
  };
}
```

### Error Handling
```javascript
async function fetchEntitiesWithErrorHandling(filters) {
  try {
    const params = buildFiltersFromForm(filters);
    const response = await fetch(`/entities?${params}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch entities:', error);
    // Show user-friendly error message
    showErrorToast('Failed to load results. Please try again.');
    return { data: [], total: 0 };
  }
}
```

## 🚀 Benefits of New API

1. **Better Performance** - Optimized database queries
2. **Easier Debugging** - Clear, readable URLs
3. **Better Caching** - URLs are more cacheable
4. **Standard REST Design** - Industry best practices
5. **Simpler Integration** - No JSON parsing/stringifying needed
6. **Better Error Messages** - Field-specific validation errors

## 🧪 Testing Examples

```bash
# Test tool filters
curl "http://localhost:3000/entities?has_api=true&technical_levels=BEGINNER&limit=5"

# Test course filters  
curl "http://localhost:3000/entities?skill_levels=INTERMEDIATE&certificate_available=true&limit=5"

# Test job filters
curl "http://localhost:3000/entities?employment_types=FULL_TIME&location_types=Remote&salary_min=50&limit=5"

# Test event filters
curl "http://localhost:3000/entities?is_online=true&event_types=Conference&limit=5"

# Test mixed filters
curl "http://localhost:3000/entities?has_api=true&certificate_available=true&is_online=true&limit=10"
```

## ✅ Action Items for Frontend Team

1. **Update API service layer** - Replace nested JSON with flat parameters
2. **Remove "Coming Soon" notices** - All filters are now functional
3. **Update form handling** - Use URLSearchParams for parameter building
4. **Test all filter combinations** - Verify functionality across entity types
5. **Update URL state management** - Support bookmarking/sharing filtered results
6. **Add proper error handling** - Handle validation errors gracefully

The backend is ready for immediate integration! 🎉
