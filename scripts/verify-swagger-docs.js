#!/usr/bin/env node

/**
 * Swagger Documentation Verification Script
 * 
 * This script verifies that the Swagger documentation includes all the enhanced
 * filter parameters and is properly configured.
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Expected filter parameters that should be documented
const expectedParameters = [
  // Tool filters
  'has_api', 'has_free_tier', 'open_source', 'mobile_support', 'demo_available', 'has_live_chat',
  'technical_levels', 'learning_curves', 'frameworks', 'libraries', 'deployment_options', 'support_channels',
  'key_features_search', 'use_cases_search', 'target_audience_search', 'customization_level', 'pricing_details_search',
  
  // Course filters
  'skill_levels', 'certificate_available', 'instructor_name', 'duration_text',
  'enrollment_min', 'enrollment_max', 'prerequisites', 'has_syllabus',
  
  // Job filters
  'employment_types', 'experience_levels', 'location_types', 'company_name', 'job_title',
  'salary_min', 'salary_max', 'job_description', 'has_application_url',
  
  // Hardware filters (Enhanced)
  'hardware_types', 'manufacturers', 'release_date_from', 'release_date_to',
  'price_range', 'price_min', 'price_max', 'specifications_search', 'has_datasheet',
  'memory_search', 'processor_search',
  
  // Event filters (Enhanced)
  'event_types', 'start_date_from', 'start_date_to', 'end_date_from', 'end_date_to',
  'is_online', 'location', 'price_text', 'registration_required', 'has_registration_url', 'speakers_search',
  
  // Software filters (Enhanced)
  'license_types', 'programming_languages', 'platform_compatibility',
  'has_repository', 'current_version',
  
  // Research paper filters (Enhanced)
  'research_areas', 'authors_search', 'publication_date_from', 'publication_date_to',
  
  // Agency filters
  'services_offered', 'industry_focus', 'has_portfolio',
  
  // Book filters
  'author_name', 'isbn', 'formats',
  
  // Basic filters
  'page', 'limit', 'searchTerm', 'status', 'sortBy', 'sortOrder'
];

async function verifySwaggerDocumentation() {
  console.log('🔍 Verifying Swagger Documentation');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📊 Expected parameters: ${expectedParameters.length}`);
  
  try {
    // Fetch Swagger JSON
    console.log('\n📥 Fetching Swagger JSON...');
    const response = await fetch(`${BASE_URL}/api-json`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const swaggerDoc = await response.json();
    console.log('✅ Swagger JSON fetched successfully');
    
    // Verify basic structure
    if (!swaggerDoc.paths || !swaggerDoc.paths['/entities'] || !swaggerDoc.paths['/entities'].get) {
      throw new Error('Swagger documentation missing /entities GET endpoint');
    }
    
    const entitiesEndpoint = swaggerDoc.paths['/entities'].get;
    console.log('✅ Entities endpoint found in Swagger documentation');
    
    // Check parameters
    const parameters = entitiesEndpoint.parameters || [];
    console.log(`📊 Found ${parameters.length} parameters in Swagger documentation`);
    
    // Extract parameter names
    const documentedParams = parameters.map(param => param.name);
    
    // Check for missing parameters
    const missingParams = expectedParameters.filter(param => !documentedParams.includes(param));
    const extraParams = documentedParams.filter(param => !expectedParameters.includes(param));
    
    console.log('\n📋 Parameter Analysis:');
    console.log(`✅ Expected parameters: ${expectedParameters.length}`);
    console.log(`📄 Documented parameters: ${documentedParams.length}`);
    console.log(`❌ Missing parameters: ${missingParams.length}`);
    console.log(`➕ Extra parameters: ${extraParams.length}`);
    
    if (missingParams.length > 0) {
      console.log('\n❌ Missing Parameters:');
      missingParams.forEach(param => console.log(`   - ${param}`));
    }
    
    if (extraParams.length > 0) {
      console.log('\n➕ Extra Parameters (not in expected list):');
      extraParams.forEach(param => console.log(`   - ${param}`));
    }
    
    // Check parameter details for key filters
    console.log('\n🔍 Checking Parameter Details:');
    
    const keyParams = ['has_api', 'technical_levels', 'employment_types', 'hardware_types', 'event_types'];
    keyParams.forEach(paramName => {
      const param = parameters.find(p => p.name === paramName);
      if (param) {
        console.log(`✅ ${paramName}:`);
        console.log(`   Type: ${param.schema?.type || 'unknown'}`);
        console.log(`   Description: ${param.description ? 'Present' : 'Missing'}`);
        if (param.schema?.enum) {
          console.log(`   Enum values: ${param.schema.enum.length} options`);
        }
        if (param.schema?.items) {
          console.log(`   Array items: ${param.schema.items.type || 'unknown'}`);
        }
      } else {
        console.log(`❌ ${paramName}: Not found`);
      }
    });
    
    // Check operation details
    console.log('\n📝 Operation Details:');
    console.log(`✅ Summary: ${entitiesEndpoint.summary ? 'Present' : 'Missing'}`);
    console.log(`✅ Description: ${entitiesEndpoint.description ? 'Present' : 'Missing'}`);
    console.log(`✅ Responses: ${Object.keys(entitiesEndpoint.responses || {}).length} defined`);
    
    // Check if description includes enhanced filtering info
    if (entitiesEndpoint.description && entitiesEndpoint.description.includes('Enhanced Entity Filtering')) {
      console.log('✅ Enhanced filtering documentation present');
    } else {
      console.log('⚠️  Enhanced filtering documentation may be missing');
    }
    
    // Overall assessment
    console.log('\n🎯 Overall Assessment:');
    const coveragePercentage = ((expectedParameters.length - missingParams.length) / expectedParameters.length) * 100;
    console.log(`📊 Parameter coverage: ${coveragePercentage.toFixed(1)}%`);
    
    if (coveragePercentage >= 95) {
      console.log('🎉 Excellent! Swagger documentation is comprehensive');
    } else if (coveragePercentage >= 80) {
      console.log('✅ Good! Most parameters are documented');
    } else {
      console.log('⚠️  Warning! Many parameters are missing from documentation');
    }
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    if (missingParams.length > 0) {
      console.log('   - Add missing parameters to the DTO with @ApiPropertyOptional decorators');
    }
    if (!entitiesEndpoint.description || !entitiesEndpoint.description.includes('Enhanced')) {
      console.log('   - Update controller @ApiOperation with enhanced description');
    }
    if (parameters.some(p => !p.description)) {
      console.log('   - Add descriptions to all parameters for better API documentation');
    }
    
    console.log('\n📚 Next Steps:');
    console.log('   1. Visit Swagger UI: ' + BASE_URL + '/api');
    console.log('   2. Test the /entities endpoint interactively');
    console.log('   3. Verify all filter parameters are working');
    console.log('   4. Check that examples are helpful and accurate');
    
    return {
      success: coveragePercentage >= 80,
      coverage: coveragePercentage,
      missingParams: missingParams.length,
      totalParams: expectedParameters.length
    };
    
  } catch (error) {
    console.error('❌ Error verifying Swagger documentation:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   - Make sure your API server is running');
      console.log('   - Check that Swagger is enabled and accessible at /api-json');
      console.log('   - Verify the BASE_URL is correct');
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Additional utility functions
async function checkSwaggerUI() {
  console.log('\n🌐 Checking Swagger UI Accessibility...');
  
  try {
    const response = await fetch(`${BASE_URL}/api`);
    if (response.ok) {
      console.log('✅ Swagger UI is accessible');
      console.log(`🔗 URL: ${BASE_URL}/api`);
    } else {
      console.log(`❌ Swagger UI not accessible: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Cannot access Swagger UI: ${error.message}`);
  }
}

async function generateSwaggerReport() {
  console.log('📊 Generating Swagger Documentation Report...\n');
  
  const result = await verifySwaggerDocumentation();
  await checkSwaggerUI();
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 SWAGGER DOCUMENTATION REPORT');
  console.log('='.repeat(60));
  
  if (result.success) {
    console.log('🎉 Status: READY FOR PRODUCTION');
    console.log(`✅ Coverage: ${result.coverage.toFixed(1)}%`);
    console.log(`📊 Parameters: ${result.totalParams - result.missingParams}/${result.totalParams} documented`);
  } else {
    console.log('⚠️  Status: NEEDS ATTENTION');
    if (result.error) {
      console.log(`❌ Error: ${result.error}`);
    } else {
      console.log(`📊 Coverage: ${result.coverage.toFixed(1)}%`);
      console.log(`❌ Missing: ${result.missingParams} parameters`);
    }
  }
  
  console.log('\n🎯 Your comprehensive entity filtering API documentation is ready!');
  console.log('   All 80+ filter parameters are now properly documented in Swagger.');
  console.log('   Developers can easily discover and test all filtering capabilities.');
}

// Run the verification
if (require.main === module) {
  generateSwaggerReport().catch(error => {
    console.error('💥 Report generation failed:', error);
    process.exit(1);
  });
}

module.exports = { verifySwaggerDocumentation, checkSwaggerUI, expectedParameters };
