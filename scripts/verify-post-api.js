#!/usr/bin/env node

/**
 * POST API Verification Script
 * 
 * This script verifies that the POST entities API supports all the enhanced
 * fields and properly validates the new enum types and array fields.
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const JWT_TOKEN = process.env.JWT_TOKEN || 'your-jwt-token-here';

// Test entity creation payloads
const testPayloads = {
  hardware: {
    name: 'Test GPU Hardware',
    website_url: 'https://example.com/gpu',
    entity_type_id: 'hardware-uuid-placeholder',
    description: 'Test GPU for AI workloads',
    hardware_details: {
      hardware_type: 'GPU',
      manufacturer: 'NVIDIA',
      release_date: '2024-01-15',
      specifications: {
        memory: '24GB GDDR6X',
        cuda_cores: 16384,
        tensor_performance: '165 TFLOPs'
      },
      datasheet_url: 'https://example.com/datasheet.pdf',
      memory: '24GB GDDR6X',
      processor: 'Ada Lovelace GPU',
      power_consumption: '450W TGP',
      use_cases: ['AI Training', 'Gaming', 'Content Creation']
    }
  },
  
  job: {
    name: 'Test AI Engineer Position',
    website_url: 'https://example.com/job',
    entity_type_id: 'job-uuid-placeholder',
    description: 'Senior AI Engineer role',
    job_details: {
      company_name: 'Tech Innovations Inc',
      employment_types: ['FULL_TIME', 'CONTRACT'],
      experience_level: 'SENIOR',
      location_types: ['Remote', 'Hybrid'],
      salary_min: 120,
      salary_max: 180,
      job_description: 'Lead AI initiatives and mentor junior developers...',
      is_remote: true,
      benefits: ['Health Insurance', 'Stock Options', 'Flexible Hours'],
      remote_policy: 'Fully Remote',
      visa_sponsorship: false,
      required_skills: ['Python', 'TensorFlow', 'Machine Learning', 'Docker']
    }
  },
  
  event: {
    name: 'Test AI Conference',
    website_url: 'https://example.com/conference',
    entity_type_id: 'event-uuid-placeholder',
    description: 'Premier AI conference',
    event_details: {
      event_type: 'Conference',
      start_date: '2024-09-15T09:00:00Z',
      end_date: '2024-09-17T17:00:00Z',
      location: 'San Francisco, CA',
      is_online: false,
      event_format: 'hybrid',
      registration_required: true,
      registration_url: 'https://example.com/register',
      capacity: 1000,
      organizer: 'AI Conference Organization',
      key_speakers: ['Dr. AI Expert', 'Prof. Machine Learning', 'Jane Innovation'],
      target_audience: ['Developers', 'Data Scientists', 'AI Researchers'],
      topics: ['Machine Learning', 'Natural Language Processing', 'Computer Vision'],
      price: '$299'
    }
  },
  
  software: {
    name: 'Test AI Software',
    website_url: 'https://example.com/software',
    entity_type_id: 'software-uuid-placeholder',
    description: 'AI development platform',
    software_details: {
      repository_url: 'https://github.com/example/ai-software',
      license_type: 'MIT',
      programming_languages: ['Python', 'JavaScript', 'TypeScript'],
      platform_compatibility: ['Windows', 'macOS', 'Linux', 'Web'],
      current_version: '2.1.0',
      release_date: '2024-01-15',
      open_source: true,
      has_free_tier: true,
      pricing_model: 'FREEMIUM'
    }
  },
  
  research_paper: {
    name: 'Test Research Paper',
    website_url: 'https://example.com/paper',
    entity_type_id: 'research-paper-uuid-placeholder',
    description: 'Groundbreaking AI research',
    research_paper_details: {
      publication_date: '2024-01-15',
      doi: '10.1000/test123',
      authors: ['Dr. AI Researcher', 'Prof. Machine Learning'],
      research_areas: ['Machine Learning', 'Natural Language Processing'],
      publication_venues: ['NeurIPS', 'ICML'],
      keywords: ['transformer', 'attention mechanism', 'neural network'],
      arxiv_id: '2401.12345',
      abstract: 'This paper explores novel approaches to transformer architectures...',
      journal_or_conference: 'NeurIPS 2024',
      pdf_url: 'https://arxiv.org/pdf/2401.12345.pdf',
      citation_count: 0
    }
  }
};

// Validation test cases
const validationTests = {
  invalid_hardware_type: {
    ...testPayloads.hardware,
    hardware_details: {
      ...testPayloads.hardware.hardware_details,
      hardware_type: 'INVALID_TYPE'
    }
  },
  
  invalid_employment_type: {
    ...testPayloads.job,
    job_details: {
      ...testPayloads.job.job_details,
      employment_types: ['INVALID_TYPE']
    }
  },
  
  invalid_event_format: {
    ...testPayloads.event,
    event_details: {
      ...testPayloads.event.event_details,
      event_format: 'invalid_format'
    }
  }
};

async function testEntityCreation(entityType, payload, shouldSucceed = true) {
  console.log(`\n🧪 Testing ${entityType} entity creation...`);
  
  try {
    const response = await fetch(`${BASE_URL}/entities`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${JWT_TOKEN}`
      },
      body: JSON.stringify(payload)
    });
    
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { error: 'Invalid JSON response', body: responseText };
    }
    
    if (shouldSucceed) {
      if (response.status === 201) {
        console.log(`✅ ${entityType} creation successful`);
        console.log(`   Entity ID: ${responseData.id || 'N/A'}`);
        console.log(`   Status: ${responseData.status || 'N/A'}`);
        return { success: true, data: responseData };
      } else {
        console.log(`❌ ${entityType} creation failed: HTTP ${response.status}`);
        console.log(`   Error: ${JSON.stringify(responseData, null, 2)}`);
        return { success: false, error: responseData };
      }
    } else {
      // This test should fail (validation test)
      if (response.status >= 400) {
        console.log(`✅ ${entityType} validation correctly rejected invalid data`);
        console.log(`   HTTP ${response.status}: ${responseData.message || responseData.error || 'Validation failed'}`);
        return { success: true, validationWorking: true };
      } else {
        console.log(`❌ ${entityType} validation failed - invalid data was accepted`);
        return { success: false, validationFailed: true };
      }
    }
  } catch (error) {
    console.log(`❌ ${entityType} test failed with network error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function checkSwaggerDocumentation() {
  console.log('\n📚 Checking POST API Swagger Documentation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api-json`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const swaggerDoc = await response.json();
    const postEndpoint = swaggerDoc.paths?.['/entities']?.post;
    
    if (!postEndpoint) {
      console.log('❌ POST /entities endpoint not found in Swagger documentation');
      return false;
    }
    
    console.log('✅ POST endpoint found in Swagger documentation');
    
    // Check if the operation has enhanced description
    if (postEndpoint.description && postEndpoint.description.includes('Enhanced Entity Creation')) {
      console.log('✅ Enhanced documentation present');
    } else {
      console.log('⚠️  Enhanced documentation may be missing');
    }
    
    // Check request body schema
    const requestBody = postEndpoint.requestBody;
    if (requestBody && requestBody.content && requestBody.content['application/json']) {
      console.log('✅ Request body schema properly defined');
      
      const schema = requestBody.content['application/json'].schema;
      if (schema.$ref && schema.$ref.includes('CreateEntityDto')) {
        console.log('✅ CreateEntityDto schema referenced correctly');
      }
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Error checking Swagger documentation: ${error.message}`);
    return false;
  }
}

async function runPostApiTests() {
  console.log('🚀 Starting POST API Enhancement Verification');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 JWT Token: ${JWT_TOKEN ? 'Provided' : 'Missing (tests will fail)'}`);
  
  if (!JWT_TOKEN || JWT_TOKEN === 'your-jwt-token-here') {
    console.log('\n⚠️  WARNING: No valid JWT token provided');
    console.log('   Set JWT_TOKEN environment variable to test entity creation');
    console.log('   Example: JWT_TOKEN=your-actual-token node scripts/verify-post-api.js');
    console.log('\n   Continuing with documentation checks only...\n');
  }
  
  // Check Swagger documentation
  const swaggerOk = await checkSwaggerDocumentation();
  
  if (!JWT_TOKEN || JWT_TOKEN === 'your-jwt-token-here') {
    console.log('\n📋 POST API Documentation Verification Complete');
    console.log(`✅ Swagger documentation: ${swaggerOk ? 'Ready' : 'Needs attention'}`);
    console.log('\n💡 To test entity creation, provide a valid JWT token and run again');
    return;
  }
  
  // Test entity creation for each type
  console.log('\n' + '='.repeat(60));
  console.log('🧪 ENTITY CREATION TESTS');
  console.log('='.repeat(60));
  
  const results = {};
  
  for (const [entityType, payload] of Object.entries(testPayloads)) {
    results[entityType] = await testEntityCreation(entityType, payload, true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
  }
  
  // Test validation
  console.log('\n' + '='.repeat(60));
  console.log('🛡️  VALIDATION TESTS');
  console.log('='.repeat(60));
  
  for (const [testName, payload] of Object.entries(validationTests)) {
    results[testName] = await testEntityCreation(testName, payload, false);
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Generate report
  console.log('\n' + '='.repeat(60));
  console.log('📊 POST API VERIFICATION REPORT');
  console.log('='.repeat(60));
  
  const successful = Object.values(results).filter(r => r.success).length;
  const total = Object.keys(results).length;
  
  console.log(`\n📈 Summary:`);
  console.log(`   Successful tests: ${successful}/${total}`);
  console.log(`   Swagger documentation: ${swaggerOk ? 'Ready' : 'Needs attention'}`);
  
  console.log(`\n🎯 Entity Creation Tests:`);
  Object.entries(testPayloads).forEach(([type]) => {
    const result = results[type];
    const status = result?.success ? '✅' : '❌';
    console.log(`   ${status} ${type} entity creation`);
  });
  
  console.log(`\n🛡️  Validation Tests:`);
  Object.keys(validationTests).forEach(testName => {
    const result = results[testName];
    const status = result?.success ? '✅' : '❌';
    console.log(`   ${status} ${testName} validation`);
  });
  
  if (successful === total && swaggerOk) {
    console.log('\n🎉 All tests passed! POST API is ready for production.');
  } else {
    console.log('\n⚠️  Some tests failed. Review the results above.');
  }
  
  console.log('\n🎯 Your enhanced POST API now supports:');
  console.log('   ✅ All enhanced entity fields');
  console.log('   ✅ Proper enum validation');
  console.log('   ✅ Array-based parameters');
  console.log('   ✅ Comprehensive documentation');
  console.log('   ✅ Type-safe entity creation');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runPostApiTests().catch(error => {
    console.error('💥 POST API verification failed:', error);
    process.exit(1);
  });
}

module.exports = { testPayloads, validationTests, testEntityCreation, runPostApiTests };
