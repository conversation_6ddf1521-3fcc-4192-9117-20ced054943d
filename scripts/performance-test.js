#!/usr/bin/env node

/**
 * Performance Test Suite for Enhanced Entity Filtering API
 * 
 * This script tests the performance of various filter combinations
 * and identifies potential bottlenecks.
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Performance test scenarios
const performanceTests = [
  {
    name: 'Simple Tool Filter',
    url: `${BASE_URL}/entities?has_api=true&limit=20`,
    expectedMaxTime: 1000, // 1 second
    description: 'Basic boolean filter on tool entities'
  },
  {
    name: 'Multiple Array Filters',
    url: `${BASE_URL}/entities?technical_levels=BEGINNER&technical_levels=INTERMEDIATE&frameworks=TensorFlow&frameworks=PyTorch&limit=20`,
    expectedMaxTime: 1500,
    description: 'Multiple array-based filters'
  },
  {
    name: 'Cross-Entity Complex Filter',
    url: `${BASE_URL}/entities?has_api=true&certificate_available=true&employment_types=FULL_TIME&hardware_types=GPU&research_areas=Machine%20Learning&limit=20`,
    expectedMaxTime: 2000,
    description: 'Complex filtering across multiple entity types'
  },
  {
    name: 'Date Range Filters',
    url: `${BASE_URL}/entities?start_date_from=2024-01-01&start_date_to=2024-12-31&publication_date_from=2020-01-01&publication_date_to=2024-12-31&limit=20`,
    expectedMaxTime: 1500,
    description: 'Date range filtering on events and research papers'
  },
  {
    name: 'Text Search Filters',
    url: `${BASE_URL}/entities?key_features_search=natural%20language&job_description=machine%20learning&speakers_search=AI&limit=20`,
    expectedMaxTime: 2000,
    description: 'Text-based search across multiple fields'
  },
  {
    name: 'Numeric Range Filters',
    url: `${BASE_URL}/entities?salary_min=80&salary_max=150&price_min=500&price_max=2000&enrollment_min=100&enrollment_max=10000&limit=20`,
    expectedMaxTime: 1500,
    description: 'Numeric range filtering'
  },
  {
    name: 'Large Result Set',
    url: `${BASE_URL}/entities?limit=100`,
    expectedMaxTime: 3000,
    description: 'Large result set without filters'
  },
  {
    name: 'Pagination Performance',
    url: `${BASE_URL}/entities?page=10&limit=20`,
    expectedMaxTime: 1000,
    description: 'Deep pagination performance'
  }
];

// Load test scenarios
const loadTests = [
  {
    name: 'Concurrent Simple Requests',
    concurrent: 10,
    url: `${BASE_URL}/entities?has_api=true&limit=10`,
    description: 'Multiple concurrent simple requests'
  },
  {
    name: 'Concurrent Complex Requests',
    concurrent: 5,
    url: `${BASE_URL}/entities?has_api=true&technical_levels=BEGINNER&certificate_available=true&employment_types=FULL_TIME&limit=20`,
    description: 'Multiple concurrent complex requests'
  },
  {
    name: 'High Concurrency Simple',
    concurrent: 50,
    url: `${BASE_URL}/entities?has_api=true&limit=5`,
    description: 'High concurrency with simple filters'
  }
];

// Performance metrics collector
class PerformanceMetrics {
  constructor() {
    this.metrics = [];
  }

  addMetric(testName, duration, success, responseSize = 0) {
    this.metrics.push({
      testName,
      duration,
      success,
      responseSize,
      timestamp: new Date().toISOString()
    });
  }

  getStats(testName) {
    const testMetrics = this.metrics.filter(m => m.testName === testName && m.success);
    if (testMetrics.length === 0) return null;

    const durations = testMetrics.map(m => m.duration);
    const responseSizes = testMetrics.map(m => m.responseSize);

    return {
      count: testMetrics.length,
      avgDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      p95Duration: this.percentile(durations, 95),
      p99Duration: this.percentile(durations, 99),
      avgResponseSize: responseSizes.reduce((sum, s) => sum + s, 0) / responseSizes.length,
      successRate: (testMetrics.length / this.metrics.filter(m => m.testName === testName).length) * 100
    };
  }

  percentile(arr, p) {
    const sorted = arr.sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }

  generateReport() {
    const report = {
      summary: {
        totalTests: this.metrics.length,
        successfulTests: this.metrics.filter(m => m.success).length,
        failedTests: this.metrics.filter(m => !m.success).length,
        overallSuccessRate: (this.metrics.filter(m => m.success).length / this.metrics.length) * 100
      },
      testResults: {}
    };

    const testNames = [...new Set(this.metrics.map(m => m.testName))];
    testNames.forEach(testName => {
      report.testResults[testName] = this.getStats(testName);
    });

    return report;
  }
}

// Test execution functions
async function runPerformanceTest(test, metrics) {
  console.log(`\n🏃 Running: ${test.name}`);
  console.log(`📝 Description: ${test.description}`);
  console.log(`🎯 Expected max time: ${test.expectedMaxTime}ms`);
  
  const startTime = performance.now();
  
  try {
    const response = await fetch(test.url);
    const duration = performance.now() - startTime;
    
    if (!response.ok) {
      console.log(`❌ HTTP ${response.status}: ${response.statusText}`);
      metrics.addMetric(test.name, duration, false);
      return false;
    }
    
    const data = await response.json();
    const responseSize = JSON.stringify(data).length;
    
    metrics.addMetric(test.name, duration, true, responseSize);
    
    const status = duration <= test.expectedMaxTime ? '✅' : '⚠️';
    console.log(`${status} Duration: ${duration.toFixed(2)}ms (Expected: ≤${test.expectedMaxTime}ms)`);
    console.log(`📊 Results: ${data.total} entities, ${(responseSize / 1024).toFixed(2)}KB response`);
    
    if (duration > test.expectedMaxTime) {
      console.log(`⚠️  Performance warning: Request took longer than expected`);
    }
    
    return duration <= test.expectedMaxTime;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.log(`❌ Network error: ${error.message}`);
    metrics.addMetric(test.name, duration, false);
    return false;
  }
}

async function runLoadTest(test, metrics) {
  console.log(`\n🔥 Load Test: ${test.name}`);
  console.log(`📝 Description: ${test.description}`);
  console.log(`🔄 Concurrent requests: ${test.concurrent}`);
  
  const startTime = performance.now();
  
  const requests = Array.from({ length: test.concurrent }, async (_, index) => {
    const requestStart = performance.now();
    try {
      const response = await fetch(test.url);
      const requestDuration = performance.now() - requestStart;
      
      if (response.ok) {
        const data = await response.json();
        const responseSize = JSON.stringify(data).length;
        metrics.addMetric(`${test.name}_request_${index}`, requestDuration, true, responseSize);
        return { success: true, duration: requestDuration, size: responseSize };
      } else {
        metrics.addMetric(`${test.name}_request_${index}`, requestDuration, false);
        return { success: false, duration: requestDuration, error: response.statusText };
      }
    } catch (error) {
      const requestDuration = performance.now() - requestStart;
      metrics.addMetric(`${test.name}_request_${index}`, requestDuration, false);
      return { success: false, duration: requestDuration, error: error.message };
    }
  });
  
  const results = await Promise.all(requests);
  const totalDuration = performance.now() - startTime;
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${test.concurrent}`);
  console.log(`❌ Failed: ${failed.length}/${test.concurrent}`);
  console.log(`⏱️  Total time: ${totalDuration.toFixed(2)}ms`);
  
  if (successful.length > 0) {
    const avgDuration = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
    const maxDuration = Math.max(...successful.map(r => r.duration));
    const minDuration = Math.min(...successful.map(r => r.duration));
    
    console.log(`📊 Avg response time: ${avgDuration.toFixed(2)}ms`);
    console.log(`📊 Min/Max response time: ${minDuration.toFixed(2)}ms / ${maxDuration.toFixed(2)}ms`);
  }
  
  return successful.length === test.concurrent;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Performance Test Suite');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📊 Performance tests: ${performanceTests.length}`);
  console.log(`🔥 Load tests: ${loadTests.length}`);
  
  const metrics = new PerformanceMetrics();
  
  // Run performance tests
  console.log('\n' + '='.repeat(60));
  console.log('📈 PERFORMANCE TESTS');
  console.log('='.repeat(60));
  
  let performancePassed = 0;
  for (const test of performanceTests) {
    const success = await runPerformanceTest(test, metrics);
    if (success) performancePassed++;
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Run load tests
  console.log('\n' + '='.repeat(60));
  console.log('🔥 LOAD TESTS');
  console.log('='.repeat(60));
  
  let loadPassed = 0;
  for (const test of loadTests) {
    const success = await runLoadTest(test, metrics);
    if (success) loadPassed++;
    
    // Longer delay between load tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Generate and display report
  console.log('\n' + '='.repeat(60));
  console.log('📊 PERFORMANCE REPORT');
  console.log('='.repeat(60));
  
  const report = metrics.generateReport();
  
  console.log(`\n📈 Summary:`);
  console.log(`   Total requests: ${report.summary.totalTests}`);
  console.log(`   Successful: ${report.summary.successfulTests}`);
  console.log(`   Failed: ${report.summary.failedTests}`);
  console.log(`   Success rate: ${report.summary.overallSuccessRate.toFixed(1)}%`);
  
  console.log(`\n🎯 Test Results:`);
  console.log(`   Performance tests passed: ${performancePassed}/${performanceTests.length}`);
  console.log(`   Load tests passed: ${loadPassed}/${loadTests.length}`);
  
  // Display detailed stats for key tests
  console.log(`\n📊 Detailed Performance Stats:`);
  Object.entries(report.testResults).forEach(([testName, stats]) => {
    if (stats && !testName.includes('_request_')) {
      console.log(`\n   ${testName}:`);
      console.log(`     Avg: ${stats.avgDuration.toFixed(2)}ms`);
      console.log(`     P95: ${stats.p95Duration.toFixed(2)}ms`);
      console.log(`     P99: ${stats.p99Duration.toFixed(2)}ms`);
      console.log(`     Success rate: ${stats.successRate.toFixed(1)}%`);
    }
  });
  
  // Performance recommendations
  console.log(`\n💡 Recommendations:`);
  const slowTests = Object.entries(report.testResults).filter(([name, stats]) => 
    stats && stats.avgDuration > 2000 && !name.includes('_request_')
  );
  
  if (slowTests.length > 0) {
    console.log(`   ⚠️  Consider optimizing these slow endpoints:`);
    slowTests.forEach(([name, stats]) => {
      console.log(`     - ${name}: ${stats.avgDuration.toFixed(2)}ms avg`);
    });
  } else {
    console.log(`   ✅ All endpoints performing within acceptable limits`);
  }
  
  const overallSuccess = (performancePassed === performanceTests.length) && 
                        (loadPassed === loadTests.length) && 
                        (report.summary.overallSuccessRate >= 95);
  
  if (overallSuccess) {
    console.log('\n🎉 All performance tests passed! API is ready for production.');
  } else {
    console.log('\n⚠️  Some performance issues detected. Review the results above.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Performance test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { performanceTests, loadTests, runAllTests };
