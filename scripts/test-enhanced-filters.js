#!/usr/bin/env node

/**
 * Comprehensive API Test Script for Enhanced Entity Filtering
 * 
 * This script tests all the new filter parameters to ensure they work correctly
 * with the flat parameter API approach.
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Test cases for all entity-specific filters
const testCases = [
  // Tool Filters
  {
    name: 'Tool Filters - API Access',
    url: `${BASE_URL}/entities?has_api=true&limit=5`,
    expectedFields: ['has_api']
  },
  {
    name: 'Tool Filters - Technical Levels',
    url: `${BASE_URL}/entities?technical_levels=BEGINNER&technical_levels=INTERMEDIATE&limit=5`,
    expectedFields: ['technical_levels']
  },
  {
    name: 'Tool Filters - Multiple Features',
    url: `${BASE_URL}/entities?has_api=true&has_free_tier=true&open_source=true&mobile_support=true&limit=5`,
    expectedFields: ['has_api', 'has_free_tier', 'open_source', 'mobile_support']
  },

  // Course Filters
  {
    name: 'Course Filters - Skill Levels',
    url: `${BASE_URL}/entities?skill_levels=BEGINNER&skill_levels=INTERMEDIATE&limit=5`,
    expectedFields: ['skill_levels']
  },
  {
    name: 'Course Filters - Certificate Available',
    url: `${BASE_URL}/entities?certificate_available=true&limit=5`,
    expectedFields: ['certificate_available']
  },
  {
    name: 'Course Filters - Instructor and Duration',
    url: `${BASE_URL}/entities?instructor_name=Dr.%20Smith&duration_text=10%20hours&limit=5`,
    expectedFields: ['instructor_name', 'duration_text']
  },

  // Job Filters
  {
    name: 'Job Filters - Employment Types',
    url: `${BASE_URL}/entities?employment_types=FULL_TIME&employment_types=PART_TIME&limit=5`,
    expectedFields: ['employment_types']
  },
  {
    name: 'Job Filters - Experience Levels',
    url: `${BASE_URL}/entities?experience_levels=MID&experience_levels=SENIOR&limit=5`,
    expectedFields: ['experience_levels']
  },
  {
    name: 'Job Filters - Location Types',
    url: `${BASE_URL}/entities?location_types=Remote&location_types=Hybrid&limit=5`,
    expectedFields: ['location_types']
  },
  {
    name: 'Job Filters - Salary Range',
    url: `${BASE_URL}/entities?salary_min=80&salary_max=150&limit=5`,
    expectedFields: ['salary_min', 'salary_max']
  },
  {
    name: 'Job Filters - Company and Description',
    url: `${BASE_URL}/entities?company_name=Google&job_description=machine%20learning&limit=5`,
    expectedFields: ['company_name', 'job_description']
  },

  // Hardware Filters (Enhanced)
  {
    name: 'Hardware Filters - Hardware Types',
    url: `${BASE_URL}/entities?hardware_types=GPU&hardware_types=CPU&limit=5`,
    expectedFields: ['hardware_types']
  },
  {
    name: 'Hardware Filters - Manufacturers',
    url: `${BASE_URL}/entities?manufacturers=NVIDIA&manufacturers=Intel&limit=5`,
    expectedFields: ['manufacturers']
  },
  {
    name: 'Hardware Filters - Release Date Range',
    url: `${BASE_URL}/entities?release_date_from=2023-01-01&release_date_to=2024-12-31&limit=5`,
    expectedFields: ['release_date_from', 'release_date_to']
  },
  {
    name: 'Hardware Filters - Price Range',
    url: `${BASE_URL}/entities?price_min=500&price_max=2000&limit=5`,
    expectedFields: ['price_min', 'price_max']
  },
  {
    name: 'Hardware Filters - Specifications and Datasheet',
    url: `${BASE_URL}/entities?specifications_search=GDDR6&has_datasheet=true&limit=5`,
    expectedFields: ['specifications_search', 'has_datasheet']
  },
  {
    name: 'Hardware Filters - Memory and Processor',
    url: `${BASE_URL}/entities?memory_search=16GB&processor_search=Intel%20i9&limit=5`,
    expectedFields: ['memory_search', 'processor_search']
  },

  // Event Filters (Enhanced)
  {
    name: 'Event Filters - Event Types',
    url: `${BASE_URL}/entities?event_types=Conference&event_types=Workshop&limit=5`,
    expectedFields: ['event_types']
  },
  {
    name: 'Event Filters - Date Ranges',
    url: `${BASE_URL}/entities?start_date_from=2024-01-01&start_date_to=2024-12-31&limit=5`,
    expectedFields: ['start_date_from', 'start_date_to']
  },
  {
    name: 'Event Filters - Online and Registration',
    url: `${BASE_URL}/entities?is_online=true&registration_required=true&limit=5`,
    expectedFields: ['is_online', 'registration_required']
  },
  {
    name: 'Event Filters - Location and Speakers',
    url: `${BASE_URL}/entities?location=San%20Francisco&speakers_search=Elon%20Musk&limit=5`,
    expectedFields: ['location', 'speakers_search']
  },

  // Agency Filters
  {
    name: 'Agency Filters - Services and Industry',
    url: `${BASE_URL}/entities?services_offered=AI%20Strategy&industry_focus=Healthcare&limit=5`,
    expectedFields: ['services_offered', 'industry_focus']
  },
  {
    name: 'Agency Filters - Portfolio',
    url: `${BASE_URL}/entities?has_portfolio=true&limit=5`,
    expectedFields: ['has_portfolio']
  },

  // Software Filters (Enhanced)
  {
    name: 'Software Filters - License Types',
    url: `${BASE_URL}/entities?license_types=MIT&license_types=Apache%202.0&limit=5`,
    expectedFields: ['license_types']
  },
  {
    name: 'Software Filters - Programming Languages',
    url: `${BASE_URL}/entities?programming_languages=Python&programming_languages=JavaScript&limit=5`,
    expectedFields: ['programming_languages']
  },
  {
    name: 'Software Filters - Platform Compatibility',
    url: `${BASE_URL}/entities?platform_compatibility=Linux&platform_compatibility=Windows&limit=5`,
    expectedFields: ['platform_compatibility']
  },
  {
    name: 'Software Filters - Repository and Version',
    url: `${BASE_URL}/entities?has_repository=true&current_version=2.0&limit=5`,
    expectedFields: ['has_repository', 'current_version']
  },

  // Research Paper Filters (Enhanced)
  {
    name: 'Research Paper Filters - Research Areas',
    url: `${BASE_URL}/entities?research_areas=Machine%20Learning&research_areas=NLP&limit=5`,
    expectedFields: ['research_areas']
  },
  {
    name: 'Research Paper Filters - Authors and Publication Date',
    url: `${BASE_URL}/entities?authors_search=Geoffrey%20Hinton&publication_date_from=2020-01-01&limit=5`,
    expectedFields: ['authors_search', 'publication_date_from']
  },

  // Book Filters
  {
    name: 'Book Filters - Author and ISBN',
    url: `${BASE_URL}/entities?author_name=Andrew%20Ng&isbn=978-0262035613&limit=5`,
    expectedFields: ['author_name', 'isbn']
  },
  {
    name: 'Book Filters - Formats',
    url: `${BASE_URL}/entities?formats=eBook&formats=Audiobook&limit=5`,
    expectedFields: ['formats']
  },

  // Cross-Entity Filtering
  {
    name: 'Cross-Entity - Tools and Courses',
    url: `${BASE_URL}/entities?has_api=true&certificate_available=true&limit=5`,
    expectedFields: ['has_api', 'certificate_available']
  },
  {
    name: 'Cross-Entity - Jobs and Events',
    url: `${BASE_URL}/entities?employment_types=FULL_TIME&is_online=true&limit=5`,
    expectedFields: ['employment_types', 'is_online']
  },
  {
    name: 'Cross-Entity - Hardware and Software',
    url: `${BASE_URL}/entities?hardware_types=GPU&programming_languages=Python&limit=5`,
    expectedFields: ['hardware_types', 'programming_languages']
  },
  {
    name: 'Cross-Entity - Complex Multi-Filter',
    url: `${BASE_URL}/entities?has_api=true&technical_levels=BEGINNER&employment_types=FULL_TIME&research_areas=Machine%20Learning&limit=10`,
    expectedFields: ['has_api', 'technical_levels', 'employment_types', 'research_areas']
  }
];

// Function to test a single API endpoint
async function testEndpoint(testCase) {
  try {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    console.log(`📡 URL: ${testCase.url}`);
    
    const response = await fetch(testCase.url);
    
    if (!response.ok) {
      console.log(`❌ HTTP ${response.status}: ${response.statusText}`);
      const errorText = await response.text();
      console.log(`   Error details: ${errorText}`);
      return false;
    }
    
    const data = await response.json();
    
    // Validate response structure
    if (!data.hasOwnProperty('data') || !data.hasOwnProperty('total')) {
      console.log(`❌ Invalid response structure`);
      console.log(`   Expected: { data: [], total: number }`);
      console.log(`   Received: ${JSON.stringify(data, null, 2)}`);
      return false;
    }
    
    console.log(`✅ Success: ${data.total} entities found`);
    console.log(`   Response time: ${response.headers.get('x-response-time') || 'N/A'}`);
    
    // Log first entity if available
    if (data.data.length > 0) {
      const firstEntity = data.data[0];
      console.log(`   Sample entity: ${firstEntity.name} (${firstEntity.entityType?.name || 'Unknown type'})`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Network error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Enhanced Entity Filter API Tests');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📊 Total test cases: ${testCases.length}`);
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    const success = await testEndpoint(testCase);
    if (success) {
      passed++;
    } else {
      failed++;
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📈 Test Results Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Success Rate: ${((passed / testCases.length) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The enhanced filtering API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error details above.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { testCases, testEndpoint, runAllTests };
