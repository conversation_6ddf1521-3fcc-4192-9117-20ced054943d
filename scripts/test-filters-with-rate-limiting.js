#!/usr/bin/env node

/**
 * Rate-Limited Entity Filter Testing Script
 * 
 * This script tests entity filters while respecting the API rate limits
 * (20 requests per minute = 1 request every 3 seconds)
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Focused test cases for validation debugging
const priorityTestCases = [
  // Working filters (baseline)
  {
    name: 'Baseline - Basic API Access Filter',
    url: `${BASE_URL}/entities?has_api=true&limit=2`,
    expectedToWork: true
  },
  {
    name: 'Baseline - Certificate Available Filter',
    url: `${BASE_URL}/entities?certificate_available=true&limit=2`,
    expectedToWork: true
  },
  
  // Previously failing filters - need investigation
  {
    name: 'Employment Types - Single Value',
    url: `${BASE_URL}/entities?employment_types=FULL_TIME&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Employment Types - Multiple Values',
    url: `${BASE_URL}/entities?employment_types=FULL_TIME&employment_types=PART_TIME&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Hardware Types - Single Value',
    url: `${BASE_URL}/entities?hardware_types=GPU&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Hardware Types - Multiple Values',
    url: `${BASE_URL}/entities?hardware_types=GPU&hardware_types=CPU&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Manufacturers Filter',
    url: `${BASE_URL}/entities?manufacturers=NVIDIA&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Company Name Search',
    url: `${BASE_URL}/entities?company_name=Google&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Job Description Search',
    url: `${BASE_URL}/entities?job_description=machine%20learning&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Release Date Range',
    url: `${BASE_URL}/entities?release_date_from=2023-01-01&release_date_to=2024-12-31&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Specifications Search',
    url: `${BASE_URL}/entities?specifications_search=GDDR6&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Has Datasheet Filter',
    url: `${BASE_URL}/entities?has_datasheet=true&limit=2`,
    expectedToWork: true,
    notes: 'Previously failed with 400 validation error'
  },
  {
    name: 'Event Online Filter',
    url: `${BASE_URL}/entities?is_online=true&limit=2`,
    expectedToWork: true,
    notes: 'Need to test this boolean filter'
  }
];

// Function to test a single API endpoint with detailed error analysis
async function testEndpointDetailed(testCase) {
  try {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    console.log(`📡 URL: ${testCase.url}`);
    if (testCase.notes) {
      console.log(`📝 Notes: ${testCase.notes}`);
    }
    
    const response = await fetch(testCase.url);
    const responseText = await response.text();
    
    if (!response.ok) {
      console.log(`❌ HTTP ${response.status}: ${response.statusText}`);
      
      // Try to parse error details
      try {
        const errorData = JSON.parse(responseText);
        console.log(`   Error Message: ${errorData.message || 'No message'}`);
        console.log(`   Error Type: ${errorData.error || 'Unknown'}`);
        if (errorData.details) {
          console.log(`   Details: ${JSON.stringify(errorData.details, null, 4)}`);
        }
        if (errorData.statusCode === 429) {
          console.log(`   ⏰ Rate limited - will wait longer before next request`);
          return { success: false, rateLimited: true };
        }
      } catch (e) {
        console.log(`   Raw error: ${responseText}`);
      }
      return { success: false, error: responseText };
    }
    
    const data = JSON.parse(responseText);
    
    // Validate response structure
    if (!data.hasOwnProperty('data') || !data.hasOwnProperty('total')) {
      console.log(`❌ Invalid response structure`);
      console.log(`   Expected: { data: [], total: number }`);
      console.log(`   Received keys: ${Object.keys(data).join(', ')}`);
      return { success: false, error: 'Invalid response structure' };
    }
    
    console.log(`✅ Success: ${data.total} entities found`);
    
    // Log sample entity if available
    if (data.data.length > 0) {
      const firstEntity = data.data[0];
      console.log(`   Sample: ${firstEntity.name} (${firstEntity.entityType?.name || 'Unknown type'})`);
    }
    
    return { success: true, data: data };
  } catch (error) {
    console.log(`❌ Network error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Main test runner with rate limiting
async function runRateLimitedTests() {
  console.log('🚀 Starting Rate-Limited Entity Filter Validation');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📊 Priority test cases: ${priorityTestCases.length}`);
  console.log(`⏰ Rate limit: 20 requests/minute (3 second intervals)\n`);
  
  let passed = 0;
  let failed = 0;
  let rateLimited = 0;
  const results = [];
  
  for (let i = 0; i < priorityTestCases.length; i++) {
    const testCase = priorityTestCases[i];
    const result = await testEndpointDetailed(testCase);
    
    results.push({
      testCase: testCase.name,
      success: result.success,
      error: result.error,
      rateLimited: result.rateLimited
    });
    
    if (result.success) {
      passed++;
    } else if (result.rateLimited) {
      rateLimited++;
      // Wait extra time if rate limited
      console.log(`   ⏰ Waiting 10 seconds due to rate limit...`);
      await new Promise(resolve => setTimeout(resolve, 10000));
    } else {
      failed++;
    }
    
    // Standard delay between requests (3 seconds to respect 20/minute limit)
    if (i < priorityTestCases.length - 1) {
      console.log(`   ⏳ Waiting 3 seconds before next test...`);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // Generate detailed report
  console.log('\n' + '='.repeat(60));
  console.log('📊 DETAILED TEST RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n📈 Summary:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⏰ Rate Limited: ${rateLimited}`);
  console.log(`📊 Success Rate: ${((passed / priorityTestCases.length) * 100).toFixed(1)}%`);
  
  console.log(`\n🔍 Detailed Results:`);
  results.forEach((result, index) => {
    const status = result.success ? '✅' : (result.rateLimited ? '⏰' : '❌');
    console.log(`${status} ${result.testCase}`);
    if (!result.success && !result.rateLimited && result.error) {
      console.log(`    Error: ${result.error.substring(0, 100)}...`);
    }
  });
  
  // Identify validation issues
  const validationErrors = results.filter(r => !r.success && !r.rateLimited);
  if (validationErrors.length > 0) {
    console.log(`\n🚨 VALIDATION ISSUES FOUND:`);
    console.log(`${validationErrors.length} filter parameters are failing validation`);
    console.log(`These need immediate attention before production deployment.`);
  }
  
  if (passed === priorityTestCases.length) {
    console.log('\n🎉 All priority tests passed! Ready for comprehensive testing.');
  } else {
    console.log('\n⚠️  Some tests failed. Review the validation errors above.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runRateLimitedTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { priorityTestCases, testEndpointDetailed, runRateLimitedTests };
