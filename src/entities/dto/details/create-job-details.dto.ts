import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsEnum, IsArray, IsNumber, IsBoolean } from 'class-validator';
import { EmploymentTypeEnum, ExperienceLevelEnum, LocationTypeEnum } from 'generated/prisma';

export class CreateJobDetailsDto {
  @ApiPropertyOptional({ description: 'Title of the job', example: 'AI Engineer' })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional({ description: 'Name of the hiring company', example: 'Tech Solutions Inc.' })
  @IsOptional()
  @IsString()
  company_name?: string;

  @ApiPropertyOptional({
    description: 'Employment types for this job',
    type: [String],
    enum: EmploymentTypeEnum,
    example: [EmploymentTypeEnum.FULL_TIME, EmploymentTypeEnum.CONTRACT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EmploymentTypeEnum, { each: true })
  employment_types?: EmploymentTypeEnum[];

  @ApiPropertyOptional({
    description: 'Experience level required',
    enum: ExperienceLevelEnum,
    example: ExperienceLevelEnum.MID,
  })
  @IsOptional()
  @IsEnum(ExperienceLevelEnum)
  experience_level?: ExperienceLevelEnum;

  @ApiPropertyOptional({
    description: 'Location types for this job',
    type: [String],
    enum: LocationTypeEnum,
    example: [LocationTypeEnum.Remote, LocationTypeEnum.Hybrid],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(LocationTypeEnum, { each: true })
  location_types?: LocationTypeEnum[];

  @ApiPropertyOptional({
    description: 'Minimum salary in thousands (e.g., 80 for $80k)',
    example: 80,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  salary_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum salary in thousands (e.g., 120 for $120k)',
    example: 120,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  salary_max?: number;

  @ApiPropertyOptional({
    description: 'URL to the job application page',
    example: 'https://jobs.example.com/apply/123',
  })
  @IsOptional()
  @IsUrl()
  application_url?: string;

  @ApiPropertyOptional({
    description: 'Full description of the job responsibilities and requirements',
    example: 'Seeking an experienced AI engineer to develop cutting-edge solutions...',
  })
  @IsOptional()
  @IsString()
  job_description?: string;

  @ApiPropertyOptional({
    description: 'Whether this is a remote job',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  is_remote?: boolean;

  @ApiPropertyOptional({
    description: 'Job location (if not remote)',
    example: 'San Francisco, CA',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Job type/category',
    example: 'Software Engineering',
  })
  @IsOptional()
  @IsString()
  job_type?: string;

  @ApiPropertyOptional({
    description: 'Key responsibilities for this role',
    type: [String],
    example: ['Develop AI models', 'Collaborate with data scientists', 'Deploy ML solutions'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  key_responsibilities?: string[];

  @ApiPropertyOptional({
    description: 'Required skills for this position',
    type: [String],
    example: ['Python', 'TensorFlow', 'Machine Learning', 'Docker'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  required_skills?: string[];

  @ApiPropertyOptional({
    description: 'Benefits offered with this position',
    type: [String],
    example: ['Health Insurance', 'Stock Options', 'Remote Work', 'Flexible Hours'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  benefits?: string[];

  @ApiPropertyOptional({
    description: 'Remote work policy',
    example: 'Fully Remote',
  })
  @IsOptional()
  @IsString()
  remote_policy?: string;

  @ApiPropertyOptional({
    description: 'Whether visa sponsorship is available',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  visa_sponsorship?: boolean;
}