import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsInt,
  Min,
  IsArray,
  IsDateString, // Suitable for YYYY-MM-DD date string input
  // IsDate, // Alternatively, if Date objects are passed
} from 'class-validator';
// import { Type } from 'class-transformer'; // Needed if using @IsDate with @Type(() => Date)

export class CreateResearchPaperDetailsDto {
  @ApiPropertyOptional({
    description: 'Publication date of the research paper (YYYY-MM-DD)',
    example: '2023-03-15',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  publication_date?: Date;

  @ApiPropertyOptional({
    description: 'Digital Object Identifier (DOI) of the paper',
    example: '10.1000/xyz123',
  })
  @IsOptional()
  @IsString()
  doi?: string;

  @ApiPropertyOptional({
    description: 'List of authors',
    example: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  authors?: string[];

  @ApiPropertyOptional({
    description: 'Research areas covered in this paper',
    example: ['Machine Learning', 'Natural Language Processing', 'Computer Vision'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  research_areas?: string[];

  @ApiPropertyOptional({
    description: 'Publication venues (journals, conferences)',
    example: ['NeurIPS', 'ICML', 'Nature'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  publication_venues?: string[];

  @ApiPropertyOptional({
    description: 'Keywords associated with the paper',
    example: ['transformer', 'attention mechanism', 'neural network', 'deep learning'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({
    description: 'ArXiv ID if available',
    example: '2301.12345',
  })
  @IsOptional()
  @IsString()
  arxiv_id?: string;

  @ApiPropertyOptional({
    description: 'Abstract of the research paper',
    example: 'This paper explores novel approaches to transformer architectures...',
  })
  @IsOptional()
  @IsString()
  abstract?: string;

  @ApiPropertyOptional({
    description: 'Name of the journal or conference',
    example: 'Journal of AI Research',
  })
  @IsOptional()
  @IsString()
  journal_or_conference?: string;

  @ApiPropertyOptional({
    description: 'URL to the PDF of the paper',
    example: 'https://arxiv.org/pdf/2301.12345.pdf',
  })
  @IsOptional()
  @IsUrl()
  pdf_url?: string;

  @ApiPropertyOptional({
    description: 'Number of citations',
    example: 150,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  citation_count?: number;
}