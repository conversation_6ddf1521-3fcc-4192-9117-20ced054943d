import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsArray,
  IsDateString,
  IsBoolean,
  IsEnum,
  IsNumber,
} from 'class-validator';
import { EventFormatEnum } from 'generated/prisma';

export class CreateEventDetailsDto {
  @ApiPropertyOptional({
    description: 'Type of the event',
    example: 'Conference',
  })
  @IsOptional()
  @IsString()
  event_type?: string;

  @ApiPropertyOptional({
    description: 'Start date of the event (YYYY-MM-DD or ISO8601 DateTime)',
    example: '2024-09-15T09:00:00Z',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'End date of the event (YYYY-MM-DD or ISO8601 DateTime)',
    example: '2024-09-17T17:00:00Z',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  end_date?: Date;

  @ApiPropertyOptional({
    description: 'Location of the event (physical address or "Online")',
    example: 'San Francisco, CA',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Whether this is an online event',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  is_online?: boolean;

  @ApiPropertyOptional({
    description: 'Event format',
    enum: EventFormatEnum,
    example: EventFormatEnum.hybrid,
  })
  @IsOptional()
  @IsEnum(EventFormatEnum)
  event_format?: EventFormatEnum;

  @ApiPropertyOptional({
    description: 'Whether registration is required',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  registration_required?: boolean;

  @ApiPropertyOptional({
    description: 'URL for event registration',
    example: 'https://eventbrite.com/event/123',
  })
  @IsOptional()
  @IsUrl()
  registration_url?: string;

  @ApiPropertyOptional({
    description: 'Event capacity (maximum attendees)',
    example: 500,
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  capacity?: number;

  @ApiPropertyOptional({
    description: 'Event organizer name',
    example: 'AI Conference Organization',
  })
  @IsOptional()
  @IsString()
  organizer?: string;

  @ApiPropertyOptional({
    description: 'Key speakers at the event',
    type: [String],
    example: ['Dr. AI Expert', 'Jane Innovations', 'Prof. Machine Learning'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  key_speakers?: string[];

  @ApiPropertyOptional({
    description: 'Target audience for the event',
    type: [String],
    example: ['Developers', 'Data Scientists', 'AI Researchers', 'Business Leaders'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  target_audience?: string[];

  @ApiPropertyOptional({
    description: 'Topics covered at the event',
    type: [String],
    example: ['Machine Learning', 'Natural Language Processing', 'Computer Vision'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  topics?: string[];

  @ApiPropertyOptional({
    description: 'Price of the event (e.g., "Free", "$99")',
    example: '$99',
  })
  @IsOptional()
  @IsString()
  price?: string;
}