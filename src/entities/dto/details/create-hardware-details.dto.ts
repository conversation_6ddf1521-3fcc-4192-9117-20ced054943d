import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsObject,
  IsDateString,
  IsEnum,
  IsArray,
} from 'class-validator';
import { HardwareTypeEnum } from 'generated/prisma';

export class CreateHardwareDetailsDto {
  @ApiPropertyOptional({
    description: 'Type of hardware',
    enum: HardwareTypeEnum,
    example: HardwareTypeEnum.GPU,
  })
  @IsOptional()
  @IsEnum(HardwareTypeEnum)
  hardware_type?: HardwareTypeEnum;

  @ApiPropertyOptional({
    description: 'Manufacturer of the hardware',
    example: 'NVIDIA',
  })
  @IsOptional()
  @IsString()
  manufacturer?: string;

  @ApiPropertyOptional({
    description: 'Release date of the hardware (YYYY-MM-DD)',
    example: '2023-09-20',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  release_date?: Date;

  @ApiPropertyOptional({
    description: 'Technical specifications of the hardware (JSON object)',
    example: { memory: '24GB GDDR6X', cuda_cores: 10496, tflops: 35.6, architecture: 'Ada Lovelace' },
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  specifications?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'URL to the hardware datasheet',
    example: 'https://nvidia.com/datasheet/rtx4070.pdf',
  })
  @IsOptional()
  @IsUrl()
  datasheet_url?: string;

  @ApiPropertyOptional({
    description: 'Price range of the hardware (e.g., "$500 - $1000")',
    example: '$699 - $799',
  })
  @IsOptional()
  @IsString()
  price_range?: string;

  @ApiPropertyOptional({
    description: 'Memory specifications (e.g., "16GB GDDR6")',
    example: '24GB GDDR6X',
  })
  @IsOptional()
  @IsString()
  memory?: string;

  @ApiPropertyOptional({
    description: 'Processor specifications (e.g., "Intel i9-13900K")',
    example: 'Ada Lovelace GPU',
  })
  @IsOptional()
  @IsString()
  processor?: string;

  @ApiPropertyOptional({
    description: 'Storage specifications',
    example: '1TB NVMe SSD',
  })
  @IsOptional()
  @IsString()
  storage?: string;

  @ApiPropertyOptional({
    description: 'Power consumption details',
    example: '220W TGP',
  })
  @IsOptional()
  @IsString()
  power_consumption?: string;

  @ApiPropertyOptional({
    description: 'Availability status',
    example: 'In Stock',
  })
  @IsOptional()
  @IsString()
  availability?: string;

  @ApiPropertyOptional({
    description: 'Price information',
    example: '$799 MSRP',
  })
  @IsOptional()
  @IsString()
  price?: string;

  @ApiPropertyOptional({
    description: 'GPU specifications (for GPU hardware)',
    example: 'RTX 4070 Ti',
  })
  @IsOptional()
  @IsString()
  gpu?: string;

  @ApiPropertyOptional({
    description: 'Use cases for this hardware',
    type: [String],
    example: ['Gaming', 'AI Training', 'Content Creation'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  use_cases?: string[];
}