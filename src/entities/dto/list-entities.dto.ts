import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsUUID, IsArray, IsEnum, Max, IsDate, IsBoolean, ValidateNested, IsDateString } from 'class-validator';
import {
  EntityStatus,
  Prisma,
  EmployeeCountRange,
  FundingStage,
  PricingModel,
  PriceRange,
  TechnicalLevel,
  LearningCurve,
  SkillLevel,
  EmploymentTypeEnum,
  ExperienceLevelEnum,
  HardwareTypeEnum,
} from '@generated-prisma';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

export class ListEntitiesDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    default: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    default: 10,
    type: Number,
    maximum: 100, // Example max limit
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100) // Max limit to prevent abuse
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by entity status',
    enum: EntityStatus,
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus;

  @ApiPropertyOptional({
    description: 'Filter by one or more entity type IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String], // Important for Swagger to know it's an array
    example: ['uuid-for-type1', 'uuid-for-type2'], // Example for Swagger
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true, message: 'Each entity type ID must be a valid UUID version 4.' })
  entityTypeIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more category IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-category1', 'uuid-for-category2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true })
  categoryIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more tag IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-tag1', 'uuid-for-tag2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true })
  tagIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more feature IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-feature1', 'uuid-for-feature2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true, message: 'Each feature ID must be a valid UUID version 4.' })
  featureIds?: string[];

  @ApiPropertyOptional({
    description: 'Search term to filter by (e.g., in name, short_description, description)',
    example: 'AI tool for images',
  })
  @IsOptional()
  @IsString()
  searchTerm?: string;

  @ApiPropertyOptional({
    description: 'Field to sort by - supports entity fields and computed values',
    enum: [
      'createdAt', 'updatedAt', 'name', 'foundedYear',
      'averageRating', 'reviewCount', 'saveCount',
      'relevance', 'popularity'
    ],
    example: 'averageRating',
    default: 'createdAt',
  })
  @IsOptional()
  @IsEnum([
    'createdAt', 'updatedAt', 'name', 'foundedYear',
    'averageRating', 'reviewCount', 'saveCount',
    'relevance', 'popularity'
  ])
  sortBy?: string = 'createdAt'; // Default sort field

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(Prisma.SortOrder)
  sortOrder?: Prisma.SortOrder = Prisma.SortOrder.desc; // Default sort order

  @ApiPropertyOptional({
    description: 'Filter by submitter user ID (UUID of the user who submitted the entity)',
    example: 'user-uuid-of-submitter',
  })
  @IsOptional()
  @IsUUID('4')
  submitterId?: string;

  @ApiPropertyOptional({
    description: 'Filter entities created on or after this date (YYYY-MM-DD or ISO 8601 string).',
    type: Date,
    format: 'date-time',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAtFrom?: Date;

  @ApiPropertyOptional({
    description: 'Filter entities created on or before this date (YYYY-MM-DD or ISO 8601 string).',
    type: Date,
    format: 'date-time',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAtTo?: Date;

  @ApiPropertyOptional({
    description: 'Filter entities by whether they have a free tier. Can be true or false.',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  hasFreeTier?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by one or more employee count ranges.',
    type: [String],
    enum: EmployeeCountRange,
    example: [EmployeeCountRange.C1_10, EmployeeCountRange.C11_50],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(EmployeeCountRange, { each: true })
  employeeCountRanges?: EmployeeCountRange[];

  @ApiPropertyOptional({
    description: 'Filter by one or more funding stages.',
    type: [String],
    enum: FundingStage,
    example: [FundingStage.SEED, FundingStage.SERIES_A],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(FundingStage, { each: true })
  fundingStages?: FundingStage[];

  @ApiPropertyOptional({
    description: 'Search by location summary.',
    example: 'San Francisco',
  })
  @IsOptional()
  @IsString()
  locationSearch?: string;

  @ApiPropertyOptional({
    description: 'Filter by API access (for tools).',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  apiAccess?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by one or more pricing models.',
    type: [String],
    enum: PricingModel,
    example: [PricingModel.SUBSCRIPTION, PricingModel.PAY_PER_USE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PricingModel, { each: true })
  pricingModels?: PricingModel[];

  @ApiPropertyOptional({
    description: 'Filter by one or more price ranges.',
    type: [String],
    enum: PriceRange,
    example: [PriceRange.LOW, PriceRange.MEDIUM],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PriceRange, { each: true })
  priceRanges?: PriceRange[];

  @ApiPropertyOptional({
    description: 'Filter by integrations (e.g., "GitHub", "Slack"). Provide as a comma-separated string.',
    type: [String],
    example: 'GitHub,Slack',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  integrations?: string[];

  @ApiPropertyOptional({
    description: 'Filter by supported platforms/OS (e.g., "Windows", "macOS"). Provide as a comma-separated string.',
    type: [String],
    example: 'Windows,macOS',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  platforms?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target audience (e.g., "Developers", "Marketers"). Provide as a comma-separated string.',
    type: [String],
    example: 'Developers,Marketers',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  targetAudience?: string[];

  // ========================================
  // ENHANCED CORE FILTERING - Rating & Reviews
  // ========================================

  @ApiPropertyOptional({
    description: 'Minimum average rating (1-5 scale)',
    type: Number,
    minimum: 1,
    maximum: 5,
    example: 4.0,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(5)
  rating_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum average rating (1-5 scale)',
    type: Number,
    minimum: 1,
    maximum: 5,
    example: 5.0,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(5)
  rating_max?: number;

  @ApiPropertyOptional({
    description: 'Minimum number of reviews',
    type: Number,
    minimum: 0,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  review_count_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of reviews',
    type: Number,
    minimum: 0,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  review_count_max?: number;

  // ========================================
  // ENHANCED CORE FILTERING - Affiliate & Business
  // ========================================

  @ApiPropertyOptional({
    description: 'Filter by affiliate status',
    enum: ['NONE', 'APPLIED', 'APPROVED', 'REJECTED'],
    example: 'APPROVED',
  })
  @IsOptional()
  @IsString()
  affiliate_status?: string;

  @ApiPropertyOptional({
    description: 'Filter entities that have affiliate/referral links available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_affiliate_link?: boolean;

  // ========================================
  // ENHANCED FILTERING - Entity Type Specific (Flat Parameters)
  // ========================================

  // --- Tool/AI Tool Filters ---
  @ApiPropertyOptional({
    description: 'Filter by technical level required to use the tool',
    enum: TechnicalLevel,
    isArray: true,
    example: [TechnicalLevel.BEGINNER, TechnicalLevel.INTERMEDIATE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(TechnicalLevel, { each: true })
  technical_levels?: TechnicalLevel[];

  @ApiPropertyOptional({
    description: 'Filter by learning curve difficulty',
    enum: LearningCurve,
    isArray: true,
    example: [LearningCurve.LOW, LearningCurve.MEDIUM],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(LearningCurve, { each: true })
  learning_curves?: LearningCurve[];

  @ApiPropertyOptional({
    description: 'Filter tools that have API access',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_api?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have a free tier',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that are open source',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  open_source?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have mobile support',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  mobile_support?: boolean;

  @ApiPropertyOptional({
    description: 'Filter tools that have demo available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  demo_available?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by frameworks supported',
    type: [String],
    example: ['TensorFlow', 'PyTorch', 'Hugging Face'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  frameworks?: string[];

  @ApiPropertyOptional({
    description: 'Filter by libraries supported',
    type: [String],
    example: ['OpenAI', 'Anthropic', 'Cohere'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  libraries?: string[];

  @ApiPropertyOptional({
    description: 'Search in key features (searches within the JSON array)',
    example: 'natural language processing',
  })
  @IsOptional()
  @IsString()
  key_features_search?: string;

  @ApiPropertyOptional({
    description: 'Search in use cases (searches within the JSON array)',
    example: 'content generation',
  })
  @IsOptional()
  @IsString()
  use_cases_search?: string;

  @ApiPropertyOptional({
    description: 'Search in target audience (searches within the JSON array)',
    example: 'developers',
  })
  @IsOptional()
  @IsString()
  target_audience_search?: string;

  @ApiPropertyOptional({
    description: 'Filter by deployment options',
    type: [String],
    example: ['Cloud', 'On-premise', 'Hybrid'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  deployment_options?: string[];

  @ApiPropertyOptional({
    description: 'Filter by support channels available',
    type: [String],
    example: ['Email', 'Chat', 'Phone', 'Community'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  support_channels?: string[];

  @ApiPropertyOptional({
    description: 'Filter tools that have live chat support',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_live_chat?: boolean;

  @ApiPropertyOptional({
    description: 'Search by customization level',
    example: 'high',
  })
  @IsOptional()
  @IsString()
  customization_level?: string;

  @ApiPropertyOptional({
    description: 'Search in pricing details',
    example: 'per user per month',
  })
  @IsOptional()
  @IsString()
  pricing_details_search?: string;

  // --- Course Filters ---
  @ApiPropertyOptional({
    description: 'Filter by skill levels required for the course',
    enum: SkillLevel,
    isArray: true,
    example: [SkillLevel.BEGINNER, SkillLevel.INTERMEDIATE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(SkillLevel, { each: true })
  skill_levels?: SkillLevel[];

  @ApiPropertyOptional({
    description: 'Filter by whether a certificate is available',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  certificate_available?: boolean;

  @ApiPropertyOptional({
    description: 'Search by instructor name (partial match)',
    example: 'Dr. Smith',
  })
  @IsOptional()
  @IsString()
  instructor_name?: string;

  @ApiPropertyOptional({
    description: 'Search by course duration text (partial match)',
    example: '10 hours',
  })
  @IsOptional()
  @IsString()
  duration_text?: string;

  @ApiPropertyOptional({
    description: 'Minimum enrollment count',
    type: Number,
    minimum: 0,
    example: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  enrollment_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum enrollment count',
    type: Number,
    minimum: 0,
    example: 10000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  enrollment_max?: number;

  @ApiPropertyOptional({
    description: 'Search in course prerequisites (partial match)',
    example: 'programming',
  })
  @IsOptional()
  @IsString()
  prerequisites?: string;

  @ApiPropertyOptional({
    description: 'Filter courses that have a syllabus URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_syllabus?: boolean;

  // --- Job Filters ---
  @ApiPropertyOptional({
    description: 'Filter by employment types',
    type: [String],
    enum: EmploymentTypeEnum,
    example: [EmploymentTypeEnum.FULL_TIME, EmploymentTypeEnum.PART_TIME, EmploymentTypeEnum.CONTRACT],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(EmploymentTypeEnum, { each: true })
  employment_types?: EmploymentTypeEnum[];

  @ApiPropertyOptional({
    description: 'Filter by experience levels',
    type: [String],
    example: ['Entry', 'Mid', 'Senior', 'Lead'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  experience_levels?: string[];

  @ApiPropertyOptional({
    description: 'Filter by location types',
    type: [String],
    example: ['Remote', 'On-site', 'Hybrid'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  location_types?: string[];

  @ApiPropertyOptional({
    description: 'Search by company name (partial match)',
    example: 'Google',
  })
  @IsOptional()
  @IsString()
  company_name?: string;

  @ApiPropertyOptional({
    description: 'Search by job title (partial match)',
    example: 'AI Engineer',
  })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional({
    description: 'Minimum salary (in thousands, e.g., 80 for $80k)',
    type: Number,
    minimum: 0,
    maximum: 1000,
    example: 80,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(1000)
  salary_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum salary (in thousands, e.g., 150 for $150k)',
    type: Number,
    minimum: 0,
    maximum: 1000,
    example: 150,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(1000)
  salary_max?: number;

  @ApiPropertyOptional({
    description: 'Search in job description (partial match)',
    example: 'machine learning',
  })
  @IsOptional()
  @IsString()
  job_description?: string;

  @ApiPropertyOptional({
    description: 'Filter jobs that have application URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_application_url?: boolean;

  // --- Hardware Filters ---
  @ApiPropertyOptional({
    description: 'Filter by hardware types',
    type: [String],
    enum: HardwareTypeEnum,
    example: [HardwareTypeEnum.GPU, HardwareTypeEnum.CPU, HardwareTypeEnum.FPGA],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(HardwareTypeEnum, { each: true })
  hardware_types?: HardwareTypeEnum[];

  @ApiPropertyOptional({
    description: 'Filter by manufacturers',
    type: [String],
    example: ['NVIDIA', 'Intel', 'AMD', 'Apple'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  manufacturers?: string[];

  @ApiPropertyOptional({
    description: 'Filter by release date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  release_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by release date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  release_date_to?: string;

  @ApiPropertyOptional({
    description: 'Search in price range text (partial match)',
    example: '$500',
  })
  @IsOptional()
  @IsString()
  price_range?: string;

  @ApiPropertyOptional({
    description: 'Minimum price (in dollars)',
    type: Number,
    minimum: 0,
    example: 500,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  price_min?: number;

  @ApiPropertyOptional({
    description: 'Maximum price (in dollars)',
    type: Number,
    minimum: 0,
    example: 2000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  price_max?: number;

  @ApiPropertyOptional({
    description: 'Search in specifications (searches within the JSON object)',
    example: 'GDDR6',
  })
  @IsOptional()
  @IsString()
  specifications_search?: string;

  @ApiPropertyOptional({
    description: 'Filter hardware that has datasheet URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_datasheet?: boolean;

  @ApiPropertyOptional({
    description: 'Search by memory specifications (partial match)',
    example: '16GB',
  })
  @IsOptional()
  @IsString()
  memory_search?: string;

  @ApiPropertyOptional({
    description: 'Search by processor specifications (partial match)',
    example: 'Intel i7',
  })
  @IsOptional()
  @IsString()
  processor_search?: string;

  // --- Event Filters ---
  @ApiPropertyOptional({
    description: 'Filter by event types',
    type: [String],
    example: ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  event_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter events starting from this date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  start_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter events starting before this date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  start_date_to?: string;

  @ApiPropertyOptional({
    description: 'Filter events ending from this date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  end_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter events ending before this date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  end_date_to?: string;

  @ApiPropertyOptional({
    description: 'Filter by whether the event is online',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  is_online?: boolean;

  @ApiPropertyOptional({
    description: 'Search by event location (partial match)',
    example: 'San Francisco',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Search in event price text (partial match)',
    example: 'Free',
  })
  @IsOptional()
  @IsString()
  price_text?: string;

  @ApiPropertyOptional({
    description: 'Filter events that require registration',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  registration_required?: boolean;

  @ApiPropertyOptional({
    description: 'Filter events that have registration URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_registration_url?: boolean;

  @ApiPropertyOptional({
    description: 'Search by key speakers (partial match)',
    example: 'Elon Musk',
  })
  @IsOptional()
  @IsString()
  speakers_search?: string;

  // --- Agency Filters ---
  @ApiPropertyOptional({
    description: 'Filter by services offered (searches within the JSON array)',
    type: [String],
    example: ['AI Strategy', 'Machine Learning', 'Data Science', 'Automation'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  services_offered?: string[];

  @ApiPropertyOptional({
    description: 'Filter by industry focus (searches within the JSON array)',
    type: [String],
    example: ['Healthcare', 'Finance', 'E-commerce', 'Manufacturing'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  industry_focus?: string[];

  @ApiPropertyOptional({
    description: 'Filter agencies that have portfolio URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_portfolio?: boolean;

  // --- Software Filters ---
  @ApiPropertyOptional({
    description: 'Filter by license type',
    type: [String],
    example: ['MIT', 'Apache 2.0', 'GPL', 'Commercial', 'Proprietary'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  license_types?: string[];

  @ApiPropertyOptional({
    description: 'Filter by programming languages (searches within the JSON array)',
    type: [String],
    example: ['Python', 'JavaScript', 'Java', 'C++', 'R'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  programming_languages?: string[];

  @ApiPropertyOptional({
    description: 'Filter by platform compatibility (searches within the JSON array)',
    type: [String],
    example: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  platform_compatibility?: string[];

  @ApiPropertyOptional({
    description: 'Filter software that has repository URL available',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  has_repository?: boolean;

  @ApiPropertyOptional({
    description: 'Search by current version (partial match)',
    example: '2.0',
  })
  @IsOptional()
  @IsString()
  current_version?: string;

  // --- Research Paper Filters ---
  @ApiPropertyOptional({
    description: 'Filter by research areas',
    type: [String],
    example: ['Machine Learning', 'Computer Vision', 'NLP', 'Robotics'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  research_areas?: string[];

  @ApiPropertyOptional({
    description: 'Search by authors (partial match)',
    example: 'Geoffrey Hinton',
  })
  @IsOptional()
  @IsString()
  authors_search?: string;

  @ApiPropertyOptional({
    description: 'Filter by publication date from (YYYY-MM-DD)',
    example: '2020-01-01',
  })
  @IsOptional()
  @IsDateString()
  publication_date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter by publication date to (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  publication_date_to?: string;

  // --- Book Filters ---
  @ApiPropertyOptional({
    description: 'Search by author name (partial match)',
    example: 'Andrew Ng',
  })
  @IsOptional()
  @IsString()
  author_name?: string;

  @ApiPropertyOptional({
    description: 'Search by ISBN',
    example: '978-0262035613',
  })
  @IsOptional()
  @IsString()
  isbn?: string;

  @ApiPropertyOptional({
    description: 'Filter by book format',
    type: [String],
    example: ['Hardcover', 'Paperback', 'eBook', 'Audiobook'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  formats?: string[];
}