import {
  Injectable,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  ForbiddenException,
  BadRequestException,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import {
  Entity,
  Prisma,
  User as UserModel,
  EntityStatus,
  EntityType,
  UserRole,
  ReviewStatus,
  EntityDetailsAgency,
  EntityDetailsBook,
  EntityDetailsBounty,
  EntityDetailsCommunity,
  EntityDetailsContentCreator,
  EntityDetailsCourse,
  EntityDetailsDataset,
  EntityDetailsEvent,
  EntityDetailsGrant,
  EntityDetailsHardware,
  EntityDetailsInvestor,
  EntityDetailsJob,
  EntityDetailsModel,
  EntityDetailsNews,
  EntityDetailsNewsletter,
  EntityDetailsPlatform,
  EntityDetailsPodcast,
  EntityDetailsProjectReference,
  EntityDetailsResearchPaper,
  EntityDetailsServiceProvider,
  EntityDetailsSoftware,
  EntityDetailsTool,
  AffiliateStatus,
  PricingModel,
  PriceRange,
  SkillLevel,
  LearningCurve,
  TechnicalLevel,
  EmployeeCountRange,
  FundingStage,
} from 'generated/prisma';
import { PaginatedResponse } from '../common/interfaces/paginated-response.interface';
import {
  mapCategoriesToConnectOrCreate,
  mapTagsToConnectOrCreate,
} from '../utils/prisma-helpers';
import { OpenaiService } from '../openai/openai.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';
import { generateSlug } from '../utils/slug.utils';
import { VectorSearchDto } from './dto/vector-search.dto';

export interface VectorSearchResult {
  id: string;
  name: string;
  shortDescription: string | null;
  logoUrl: string | null;
  entityTypeSlug: string;
  similarity: number;
}

// Helper function to map entity type slugs (from DB) to their corresponding detail DTO keys
const entityTypeSlugToDetailKey = (
  slug: string,
): keyof UpdateEntityDto | null => {
  const mapping: Record<string, keyof UpdateEntityDto> = {
    'ai-tool': 'tool_details',
    'online-course': 'course_details',
    'agency': 'agency_details',
    'content-creator': 'content_creator_details',
    'community': 'community_details',
    'newsletter': 'newsletter_details',
    'dataset': 'dataset_details',
    'research-paper': 'research_paper_details',
    'software': 'software_details',
    'model': 'model_details',
    'project-reference': 'project_reference_details',
    'service-provider': 'service_provider_details',
    'investor': 'investor_details',
    'event': 'event_details',
    'job': 'job_details',
    'grant': 'grant_details',
    'bounty': 'bounty_details',
    'hardware': 'hardware_details',
    'news': 'news_details',
    'book': 'book_details',
    'podcast': 'podcast_details',
    'platform': 'platform_details',
  };
  return mapping[slug] || null;
};

// Helper function to map entity type slugs (from DB) to their corresponding Prisma relation keys
const entityTypeSlugToPrismaDetailKey = (
  slug: string,
): keyof Prisma.EntityInclude | null => {
  const mapping: Record<string, keyof Prisma.EntityInclude> = {
    'ai-tool': 'entityDetailsTool',
    'course': 'entityDetailsCourse', // Updated from 'online-course' to match new entity type
    'online-course': 'entityDetailsCourse', // Keep backward compatibility
    'agency': 'entityDetailsAgency',
    'content-creator': 'entityDetailsContentCreator',
    'community': 'entityDetailsCommunity',
    'newsletter': 'entityDetailsNewsletter',
    'dataset': 'entityDetailsDataset',
    'research-paper': 'entityDetailsResearchPaper',
    'software': 'entityDetailsSoftware',
    'model': 'entityDetailsModel',
    'project-reference': 'entityDetailsProjectReference',
    'service-provider': 'entityDetailsServiceProvider',
    'investor': 'entityDetailsInvestor',
    'event': 'entityDetailsEvent',
    'job': 'entityDetailsJob',
    'grant': 'entityDetailsGrant',
    'bounty': 'entityDetailsBounty',
    'hardware': 'entityDetailsHardware',
    'news': 'entityDetailsNews',
    'book': 'entityDetailsBook',
    'podcast': 'entityDetailsPodcast',
    'platform': 'entityDetailsPlatform',
  };
  return mapping[slug] || null;
};

@Injectable()
export class EntitiesService implements OnModuleInit {
  private entityTypeMap: Map<string, string> = new Map();
  private readonly logger = new Logger(EntitiesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly openaiService: OpenaiService,
    private readonly activityLogger: ActivityLoggerService,
    private readonly validationService: ValidationService,
  ) {
    console.log('----------------------------------------------------');
    console.log('[EntitiesService] CONSTRUCTOR CALLED');
    console.log('----------------------------------------------------');
  }

  async onModuleInit() {
    console.log('----------------------------------------------------');
    console.log('[EntitiesService] ON_MODULE_INIT CALLED. Attempting to load entity types...');
    console.log('----------------------------------------------------');
    await this.loadEntityTypes();
  }

  private async loadEntityTypes() {
    console.log('[EntitiesService] loadEntityTypes - STARTING');
    try {
      const entityTypes = await this.prisma.entityType.findMany({
        select: { id: true, slug: true },
      });
      if (entityTypes && entityTypes.length > 0) {
        this.entityTypeMap.clear();
        entityTypes.forEach(type => this.entityTypeMap.set(type.id, type.slug));
        console.log(`[EntitiesService] Entity types loaded into map. Count: ${this.entityTypeMap.size}.`);
        if (this.entityTypeMap.size > 0) {
            const firstKey = this.entityTypeMap.keys().next().value;
            if (firstKey) {
                 console.log(`[EntitiesService] Sample map entry - ID: ${firstKey}, Slug: ${this.entityTypeMap.get(firstKey)}`);
            }
        }
        console.log('[EntitiesService] loadEntityTypes - FINISHED SUCCESSFULLY');
      } else {
        console.log('[EntitiesService] No entity types found in the database to load into map.');
      }
    } catch (error) {
      console.error('[EntitiesService] loadEntityTypes - CRITICAL FAILURE:', error);
      this.logger.error('[EntitiesService] CRITICAL: Failed to load entity types on startup:', error.stack);
    }
  }

  private async getUniqueSlug(name: string): Promise<string> {
    let slug = generateSlug(name);
    let count = 0;
    while (await this.prisma.entity.findUnique({ where: { slug } })) {
      count++;
      slug = `${generateSlug(name)}-${count}`;
    }
    return slug;
  }

  async generateFtsTextForEntity(entityId: string, tx: Prisma.TransactionClient): Promise<string> {
    const entityWithDetails = await tx.entity.findUniqueOrThrow({
      where: { id: entityId },
      include: {
        entityType: true,
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityFeatures: { include: { feature: true } },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
      },
    });

    let textToEmbed = `${entityWithDetails.name || ''}. ${
      entityWithDetails.shortDescription || ''
    }. ${entityWithDetails.description || ''}.`;

    if (entityWithDetails.entityCategories.length > 0) {
      textToEmbed += ` Categories: ${entityWithDetails.entityCategories
        .map(ec => ec.category.name)
        .join(', ')}.`;
    }
    if (entityWithDetails.entityTags.length > 0) {
      textToEmbed += ` Tags: ${entityWithDetails.entityTags
        .map(et => et.tag.name)
        .join(', ')}.`;
    }
    if (entityWithDetails.entityFeatures.length > 0) {
      textToEmbed += ` Features: ${entityWithDetails.entityFeatures
        .map(ef => ef.feature.name)
        .join(', ')}.`;
    }

    const details =
      entityWithDetails.entityDetailsTool ||
      entityWithDetails.entityDetailsCourse ||
      entityWithDetails.entityDetailsAgency ||
      entityWithDetails.entityDetailsContentCreator ||
      entityWithDetails.entityDetailsCommunity ||
      entityWithDetails.entityDetailsNewsletter ||
      entityWithDetails.entityDetailsDataset ||
      entityWithDetails.entityDetailsResearchPaper ||
      entityWithDetails.entityDetailsSoftware ||
      entityWithDetails.entityDetailsModel ||
      entityWithDetails.entityDetailsProjectReference ||
      entityWithDetails.entityDetailsServiceProvider ||
      entityWithDetails.entityDetailsInvestor ||
      entityWithDetails.entityDetailsEvent ||
      entityWithDetails.entityDetailsJob ||
      entityWithDetails.entityDetailsGrant ||
      entityWithDetails.entityDetailsBounty ||
      entityWithDetails.entityDetailsHardware ||
      entityWithDetails.entityDetailsNews ||
      entityWithDetails.entityDetailsBook ||
      entityWithDetails.entityDetailsPodcast ||
      entityWithDetails.entityDetailsPlatform;

    if (details) {
      if ('keyFeatures' in details && Array.isArray(details.keyFeatures)) {
        textToEmbed += ` Key Features: ${details.keyFeatures.join(', ')}.`;
      }
      if ('useCases' in details && Array.isArray(details.useCases)) {
        textToEmbed += ` Use Cases: ${details.useCases.join(', ')}.`;
      }
    }
    return textToEmbed;
  }

  async generateAndSaveEmbedding(entityId: string, tx: Prisma.TransactionClient) {
    this.logger.log(`[Embedding BG] Job Started for entity ${entityId}`);
    try {
      const textToEmbed = await this.generateFtsTextForEntity(entityId, tx);

      this.logger.log(`[Embedding BG] Text prepared for entity ${entityId}: "${textToEmbed.substring(0, 200)}..."`);
      this.logger.debug(`[Embedding BG] Full text for entity ${entityId}: ${textToEmbed}`);

      const embedding = await this.openaiService.generateEmbedding(textToEmbed);

      if (!embedding) {
        this.logger.warn(
          `[Embedding BG] OpenAI did not return an embedding for entity ${entityId}. Skipping DB update.`,
        );
        return;
      }
      this.logger.log(
        `[Embedding BG] Embedding generated successfully for entity ${entityId}. Saving to DB...`,
      );

      const vectorString = `[${embedding.join(',')}]`;
      await tx.$executeRaw`UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entityId}::uuid`;

      this.logger.log(
        `[Embedding BG] Successfully saved embedding for entity ${entityId}.`,
      );
    } catch (error) {
      this.logger.error(
        `[Embedding BG] Job FAILED for entity ${entityId}:`,
        error.stack,
      );
    }
  }

  // Helper to map DTO fields to Prisma input for tool details
  private mapToolDetailsToPrisma(toolDetails: any) {
    if (!toolDetails) return {};
    const prismaData: any = {};

    // Tool-specific fields (removed api_documentation_url as it was dropped from the schema)
    if (toolDetails.learning_curve !== undefined) prismaData.learningCurve = toolDetails.learning_curve;
    if (toolDetails.key_features !== undefined) prismaData.keyFeatures = toolDetails.key_features as Prisma.JsonArray;
    if (toolDetails.programming_languages !== undefined) prismaData.programmingLanguages = toolDetails.programming_languages as Prisma.JsonArray;
    if (toolDetails.frameworks !== undefined) prismaData.frameworks = toolDetails.frameworks as Prisma.JsonArray;
    if (toolDetails.libraries !== undefined) prismaData.libraries = toolDetails.libraries as Prisma.JsonArray;
    if (toolDetails.target_audience !== undefined) prismaData.targetAudience = toolDetails.target_audience as Prisma.JsonArray;
    if (toolDetails.deployment_options !== undefined) prismaData.deploymentOptions = toolDetails.deployment_options as Prisma.JsonArray;
    if (toolDetails.supported_os !== undefined) prismaData.supportedOs = toolDetails.supported_os as Prisma.JsonArray;
    if (toolDetails.mobile_support !== undefined) prismaData.mobileSupport = toolDetails.mobile_support;
    if (toolDetails.api_access !== undefined) prismaData.apiAccess = toolDetails.api_access;
    if (toolDetails.customization_level !== undefined) prismaData.customizationLevel = toolDetails.customization_level;
    if (toolDetails.trial_available !== undefined) prismaData.trialAvailable = toolDetails.trial_available;
    if (toolDetails.demo_available !== undefined) prismaData.demoAvailable = toolDetails.demo_available;
    if (toolDetails.open_source !== undefined) prismaData.openSource = toolDetails.open_source;
    if (toolDetails.support_channels !== undefined) prismaData.supportChannels = toolDetails.support_channels as Prisma.JsonArray;

    // Shared fields
    if (toolDetails.has_free_tier !== undefined) prismaData.hasFreeTier = toolDetails.has_free_tier;
    if (toolDetails.use_cases !== undefined) prismaData.useCases = toolDetails.use_cases as Prisma.JsonArray;
    if (toolDetails.integrations !== undefined) prismaData.integrations = toolDetails.integrations as Prisma.JsonArray;
    if (toolDetails.pricing_model !== undefined) prismaData.pricingModel = toolDetails.pricing_model;
    if (toolDetails.price_range !== undefined) prismaData.priceRange = toolDetails.price_range;
    if (toolDetails.pricing_details !== undefined) prismaData.pricingDetails = toolDetails.pricing_details;
    if (toolDetails.pricing_url !== undefined) prismaData.pricingUrl = toolDetails.pricing_url;
    if (toolDetails.support_email !== undefined) prismaData.supportEmail = toolDetails.support_email;
    if (toolDetails.has_live_chat !== undefined) prismaData.hasLiveChat = toolDetails.has_live_chat;
    if (toolDetails.community_url !== undefined) prismaData.communityUrl = toolDetails.community_url;

    return prismaData;
  }

  // Helper to map DTO fields to Prisma input for course details
  private mapCourseDetailsToPrisma(courseDetails: any) {
    if (!courseDetails) return {};
    const prismaData: any = {};

    if (courseDetails.instructor_name !== undefined) prismaData.instructorName = courseDetails.instructor_name;
    if (courseDetails.duration_text !== undefined) prismaData.durationText = courseDetails.duration_text;
    if (courseDetails.skill_level !== undefined) prismaData.skillLevel = courseDetails.skill_level;
    if (courseDetails.prerequisites !== undefined) prismaData.prerequisites = courseDetails.prerequisites;
    if (courseDetails.syllabus_url !== undefined) prismaData.syllabusUrl = courseDetails.syllabus_url;
    if (courseDetails.enrollment_count !== undefined) prismaData.enrollmentCount = courseDetails.enrollment_count;
    if (courseDetails.certificate_available !== undefined) prismaData.certificateAvailable = courseDetails.certificate_available;

    return prismaData;
  }

  // Helper to map DTO fields to Prisma input for agency details
  private mapAgencyDetailsToPrisma(agencyDetails: any) {
    if (!agencyDetails) return {};
    const prismaData: any = {};

    if (agencyDetails.services_offered !== undefined) prismaData.servicesOffered = agencyDetails.services_offered as Prisma.JsonArray;
    if (agencyDetails.industry_focus !== undefined) prismaData.industryFocus = agencyDetails.industry_focus as Prisma.JsonArray;
    if (agencyDetails.target_client_size !== undefined) prismaData.targetClientSize = agencyDetails.target_client_size as Prisma.JsonArray;
    if (agencyDetails.target_audience !== undefined) prismaData.targetAudience = agencyDetails.target_audience as Prisma.JsonArray;
    if (agencyDetails.location_summary !== undefined) prismaData.locationSummary = agencyDetails.location_summary;
    if (agencyDetails.portfolio_url !== undefined) prismaData.portfolioUrl = agencyDetails.portfolio_url;
    if (agencyDetails.pricing_info !== undefined) prismaData.pricingInfo = agencyDetails.pricing_info;

    return prismaData;
  }

  // Helper to map DTO fields to Prisma input for content creator details
  private mapContentCreatorDetailsToPrisma(contentCreatorDetails: any) {
    if (!contentCreatorDetails) return {};
    const prismaData: any = {};

    if (contentCreatorDetails.creator_name !== undefined) prismaData.creatorName = contentCreatorDetails.creator_name;
    if (contentCreatorDetails.primary_platform !== undefined) prismaData.primaryPlatform = contentCreatorDetails.primary_platform;
    if (contentCreatorDetails.focus_areas !== undefined) prismaData.focusAreas = contentCreatorDetails.focus_areas as Prisma.JsonArray;
    if (contentCreatorDetails.follower_count !== undefined) prismaData.followerCount = contentCreatorDetails.follower_count;
    if (contentCreatorDetails.example_content_url !== undefined) prismaData.exampleContentUrl = contentCreatorDetails.example_content_url;

    return prismaData;
  }

  // Helper to map DTO fields to Prisma input for community details
  private mapCommunityDetailsToPrisma(communityDetails: any) {
    if (!communityDetails) return {};
    const prismaData: any = {};

    if (communityDetails.platform !== undefined) prismaData.platform = communityDetails.platform;
    if (communityDetails.member_count !== undefined) prismaData.memberCount = communityDetails.member_count;
    if (communityDetails.focus_topics !== undefined) prismaData.focusTopics = communityDetails.focus_topics as Prisma.JsonArray;
    if (communityDetails.rules_url !== undefined) prismaData.rulesUrl = communityDetails.rules_url;
    if (communityDetails.invite_url !== undefined) prismaData.inviteUrl = communityDetails.invite_url;
    if (communityDetails.main_channel_url !== undefined) prismaData.mainChannelUrl = communityDetails.main_channel_url;

    return prismaData;
  }

  // Helper to map DTO fields to Prisma input for newsletter details
  private mapNewsletterDetailsToPrisma(newsletterDetails: any) {
    if (!newsletterDetails) return {};
    const prismaData: any = {};

    if (newsletterDetails.frequency !== undefined) prismaData.frequency = newsletterDetails.frequency;
    if (newsletterDetails.main_topics !== undefined) prismaData.mainTopics = newsletterDetails.main_topics as Prisma.JsonArray;
    if (newsletterDetails.archive_url !== undefined) prismaData.archiveUrl = newsletterDetails.archive_url;
    if (newsletterDetails.subscribe_url !== undefined) prismaData.subscribeUrl = newsletterDetails.subscribe_url;
    if (newsletterDetails.author_name !== undefined) prismaData.authorName = newsletterDetails.author_name;
    if (newsletterDetails.subscriber_count !== undefined) prismaData.subscriberCount = newsletterDetails.subscriber_count;

    return prismaData;
  }

  // Generic helper to map snake_case DTO fields to camelCase Prisma fields
  private mapGenericDetailsToPrisma(detailsDto: any) {
    if (!detailsDto) return {};
    const prismaData: any = {};

    // Convert snake_case to camelCase for common field patterns
    Object.keys(detailsDto).forEach(key => {
      if (detailsDto[key] !== undefined) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        prismaData[camelKey] = detailsDto[key];
      }
    });

    return prismaData;
  }

  // Generic helper to map any details DTO to Prisma format based on entity type
  private mapDetailsToPrisma(detailsDto: any, entityTypeSlug: string) {
    switch (entityTypeSlug) {
      case 'ai-tool':
        return this.mapToolDetailsToPrisma(detailsDto);
      case 'course':
      case 'online-course':
        return this.mapCourseDetailsToPrisma(detailsDto);
      case 'agency':
        return this.mapAgencyDetailsToPrisma(detailsDto);
      case 'content-creator':
        return this.mapContentCreatorDetailsToPrisma(detailsDto);
      case 'community':
        return this.mapCommunityDetailsToPrisma(detailsDto);
      case 'newsletter':
        return this.mapNewsletterDetailsToPrisma(detailsDto);
      // For new entity types, use generic mapping (snake_case to camelCase conversion)
      case 'hardware':
      case 'software':
      case 'research-paper':
      case 'job':
      case 'event':
      case 'podcast':
      case 'grant':
      case 'dataset':
      case 'model':
      case 'project-reference':
      case 'service-provider':
      case 'investor':
      case 'bounty':
      case 'news':
      case 'book':
      case 'platform':
        return this.mapGenericDetailsToPrisma(detailsDto);
      default:
        return detailsDto || {};
    }
  }

  async create(createEntityDto: CreateEntityDto, submitterUser: UserModel): Promise<Entity> {
    // Validate entity submission rate limits
    await this.validationService.validateEntitySubmissionRate(submitterUser.id);

    const {
      entity_type_id,
      category_ids,
      tag_ids,
      feature_ids,

      // Destructure all direct entity fields from DTO
      name, 
      website_url,
      short_description,
      description,
      logo_url,
      documentation_url,
      contact_url,
      privacy_policy_url,
      founded_year,
      social_links,
      meta_title, // New
      meta_description, // New
      employee_count_range, // New
      funding_stage, // New
      location_summary, // New
      ref_link, // New
      affiliate_status, // New
      scraped_review_sentiment_label, // New
      scraped_review_sentiment_score, // New
      scraped_review_count, // New
      
      // Destructure all detail DTOs
      tool_details,
      course_details,
      agency_details,
      content_creator_details,
      community_details,
      newsletter_details,
      dataset_details, // New
      research_paper_details, // New
      software_details, // New
      model_details, // New
      project_reference_details, // New
      service_provider_details, // New
      investor_details, // New
      event_details, // New
      job_details, // New
      grant_details, // New
      bounty_details, // New
      hardware_details, // New
      news_details, // New
      book_details, // New
      podcast_details, // New
      platform_details, // New
    } = createEntityDto;

    this.logger.log(`[EntitiesService Create] Received entity_type_id: ${entity_type_id}`);
    this.logger.log(`[EntitiesService Create] Current entityTypeMap (size ${this.entityTypeMap.size}): ${JSON.stringify(Array.from(this.entityTypeMap.entries()))}`);

    const entityTypeSlug = this.entityTypeMap.get(entity_type_id);

    if (!entityTypeSlug) {
      this.logger.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys()).join(', ')}]`);
      console.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys())}`);
      throw new BadRequestException(`Invalid entity_type_id: ${entity_type_id}`);
    }
    
    const providedDetails: { [key: string]: any } = {
        'ai-tool': tool_details,
        'course': course_details, // Updated to match new entity type slug
        'online-course': course_details, // Keep backward compatibility
        'agency': agency_details,
        'content-creator': content_creator_details,
        'community': community_details,
        'newsletter': newsletter_details,
        'dataset': dataset_details, // New
        'research-paper': research_paper_details, // New
        'software': software_details, // New
        'model': model_details, // New
        'project-reference': project_reference_details, // New
        'service-provider': service_provider_details, // New
        'investor': investor_details, // New
        'event': event_details, // New
        'job': job_details, // New
        'grant': grant_details, // New
        'bounty': bounty_details, // New
        'hardware': hardware_details, // New
        'news': news_details, // New
        'book': book_details, // New
        'podcast': podcast_details, // New
        'platform': platform_details, // New
    };

    let expectedDetailKeyForSlug = entityTypeSlugToDetailKey(entityTypeSlug);

    // Refined logic from user:
    let detailIsProvided = false;
    if (tool_details) detailIsProvided = true;
    if (course_details) detailIsProvided = true;
    if (agency_details) detailIsProvided = true;
    if (content_creator_details) detailIsProvided = true;
    if (community_details) detailIsProvided = true;
    if (newsletter_details) detailIsProvided = true;
    if (dataset_details) detailIsProvided = true; // New
    if (research_paper_details) detailIsProvided = true; // New
    if (software_details) detailIsProvided = true; // New
    if (model_details) detailIsProvided = true; // New
    if (project_reference_details) detailIsProvided = true; // New
    if (service_provider_details) detailIsProvided = true; // New
    if (investor_details) detailIsProvided = true; // New
    if (event_details) detailIsProvided = true; // New
    if (job_details) detailIsProvided = true; // New
    if (grant_details) detailIsProvided = true; // New
    if (bounty_details) detailIsProvided = true; // New
    if (hardware_details) detailIsProvided = true; // New
    if (news_details) detailIsProvided = true; // New
    if (book_details) detailIsProvided = true; // New
    if (podcast_details) detailIsProvided = true; // New
    if (platform_details) detailIsProvided = true; // New

    let correctDetailIsPresent = false;
    if (entityTypeSlug === 'ai-tool' && tool_details) correctDetailIsPresent = true;
    else if ((entityTypeSlug === 'course' || entityTypeSlug === 'online-course') && course_details) correctDetailIsPresent = true; // Support both slugs
    else if (entityTypeSlug === 'agency' && agency_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'content-creator' && content_creator_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'community' && community_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'newsletter' && newsletter_details) correctDetailIsPresent = true;
    else if (entityTypeSlug === 'dataset' && dataset_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'research-paper' && research_paper_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'software' && software_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'model' && model_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'project-reference' && project_reference_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'service-provider' && service_provider_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'investor' && investor_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'event' && event_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'job' && job_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'grant' && grant_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'bounty' && bounty_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'hardware' && hardware_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'news' && news_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'book' && book_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'podcast' && podcast_details) correctDetailIsPresent = true; // New
    else if (entityTypeSlug === 'platform' && platform_details) correctDetailIsPresent = true; // New

    if (detailIsProvided && !correctDetailIsPresent) {
       throw new BadRequestException(
         `Incorrect detail DTO provided for entity type '${entityTypeSlug}'. Expected '${expectedDetailKeyForSlug || entityTypeSlug + '_details'}'.`
       );
    }

    const actualProvidedDetailObjects = [
        tool_details, course_details, agency_details, 
        content_creator_details, community_details, newsletter_details,
        dataset_details, research_paper_details, software_details, model_details,
        project_reference_details, service_provider_details, investor_details,
        event_details, job_details, grant_details, bounty_details, hardware_details,
        news_details, book_details, podcast_details, platform_details,
    ].filter(detail => detail !== undefined);

    if (actualProvidedDetailObjects.length > 1) {
        throw new BadRequestException('Only one type of entity details can be provided alongside the main entity data.');
    }

    // Generate unique slug for the entity
    const slug = await this.getUniqueSlug(name);

    // Consolidate all direct entity fields for Prisma
    const directEntityData: Omit<Prisma.EntityCreateInput, 'entityType' | 'submitter' | 'status' | 'entityCategories' | 'entityTags' | 'entityDetailsTool' | 'entityDetailsCourse' | 'entityDetailsAgency' | 'entityDetailsContentCreator' | 'entityDetailsCommunity' | 'entityDetailsNewsletter' | 'entityDetailsDataset' | 'entityDetailsResearchPaper' | 'entityDetailsSoftware' | 'entityDetailsModel' | 'entityDetailsProjectReference' | 'entityDetailsServiceProvider' | 'entityDetailsInvestor' | 'entityDetailsEvent' | 'entityDetailsGrant' | 'entityDetailsBounty' | 'entityDetailsHardware' | 'entityDetailsNews' | 'entityDetailsBook' | 'entityDetailsPodcast' | 'entityDetailsPlatform'> = {
        name,
        slug,
        websiteUrl: website_url,
        shortDescription: short_description,
        description,
        logoUrl: logo_url,
        documentationUrl: documentation_url,
        contactUrl: contact_url,
        privacyPolicyUrl: privacy_policy_url,
        foundedYear: founded_year,
        socialLinks: social_links as Prisma.InputJsonValue | undefined,
        metaTitle: meta_title, // New
        metaDescription: meta_description, // New
        employeeCountRange: employee_count_range, // New
        fundingStage: funding_stage, // New
        locationSummary: location_summary, // New
        refLink: ref_link, // New
        affiliateStatus: affiliate_status, // New
        scrapedReviewSentimentLabel: scraped_review_sentiment_label, // New
        scrapedReviewSentimentScore: scraped_review_sentiment_score, // New
        scrapedReviewCount: scraped_review_count, // New
        // vectorEmbedding will be handled by a trigger or separate process, not directly in create DTO
    };

    // Step 1: Create the entity and its core data within a transaction
    const newEntity = await this.prisma.$transaction(async (tx) => {
        const createData: Prisma.EntityCreateInput = {
            ...directEntityData, // Spread the direct data
            entityType: { connect: { id: entity_type_id } },
            submitter: { connect: { id: submitterUser.id } },
            status: EntityStatus.PENDING, // Default status
            
            // Dynamic details based on entityTypeSlug - with proper field mapping
            ...(entityTypeSlug === 'ai-tool' && tool_details && { entityDetailsTool: { create: this.mapToolDetailsToPrisma(tool_details) } }),
            ...((entityTypeSlug === 'course' || entityTypeSlug === 'online-course') && course_details && { entityDetailsCourse: { create: this.mapCourseDetailsToPrisma(course_details) } }),
            ...(entityTypeSlug === 'agency' && agency_details && { entityDetailsAgency: { create: this.mapAgencyDetailsToPrisma(agency_details) } }),
            ...(entityTypeSlug === 'content-creator' && content_creator_details && { entityDetailsContentCreator: { create: this.mapContentCreatorDetailsToPrisma(content_creator_details) } }),
            ...(entityTypeSlug === 'community' && community_details && { entityDetailsCommunity: { create: this.mapCommunityDetailsToPrisma(community_details) } }),
            ...(entityTypeSlug === 'newsletter' && newsletter_details && { entityDetailsNewsletter: { create: this.mapNewsletterDetailsToPrisma(newsletter_details) } }),
            ...(entityTypeSlug === 'dataset' && dataset_details && { entityDetailsDataset: { create: this.mapDetailsToPrisma(dataset_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'research-paper' && research_paper_details && { entityDetailsResearchPaper: { create: this.mapDetailsToPrisma(research_paper_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'software' && software_details && { entityDetailsSoftware: { create: this.mapDetailsToPrisma(software_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'model' && model_details && { entityDetailsModel: { create: this.mapDetailsToPrisma(model_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'project-reference' && project_reference_details && { entityDetailsProjectReference: { create: this.mapDetailsToPrisma(project_reference_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'service-provider' && service_provider_details && { entityDetailsServiceProvider: { create: this.mapDetailsToPrisma(service_provider_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'investor' && investor_details && { entityDetailsInvestor: { create: this.mapDetailsToPrisma(investor_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'event' && event_details && { entityDetailsEvent: { create: this.mapDetailsToPrisma(event_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'job' && job_details && { entityDetailsJob: { create: this.mapDetailsToPrisma(job_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'grant' && grant_details && { entityDetailsGrant: { create: this.mapDetailsToPrisma(grant_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'bounty' && bounty_details && { entityDetailsBounty: { create: this.mapDetailsToPrisma(bounty_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'hardware' && hardware_details && { entityDetailsHardware: { create: this.mapDetailsToPrisma(hardware_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'news' && news_details && { entityDetailsNews: { create: this.mapDetailsToPrisma(news_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'book' && book_details && { entityDetailsBook: { create: this.mapDetailsToPrisma(book_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'podcast' && podcast_details && { entityDetailsPodcast: { create: this.mapDetailsToPrisma(podcast_details, entityTypeSlug) } }), // New
            ...(entityTypeSlug === 'platform' && platform_details && { entityDetailsPlatform: { create: this.mapDetailsToPrisma(platform_details, entityTypeSlug) } }), // New
                        
            entityCategories: category_ids
              ? { create: category_ids.map(catId => ({ categoryId: catId, assignedBy: submitterUser.id })) } 
              : undefined,
            entityTags: tag_ids
              ? { create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: submitterUser.id })) }
              : undefined,
            entityFeatures: feature_ids
              ? { create: feature_ids.map(featId => ({ featureId: featId, assignedBy: submitterUser.id })) }
              : undefined,
          };
  
          this.logger.debug(`[EntitiesService Create] createData object: ${JSON.stringify(createData, null, 2)}`);
  
          const newEntityTx = await tx.entity.create({
            data: createData,
            include: {
              entityType: true,
              submitter: { 
                select: { 
                  id: true, 
                  authUserId: true,
                  email: true,
                  createdAt: true,
                  lastLogin: true,
                  username: true,
                  displayName: true,
                  profilePictureUrl: true 
                }
              },
              entityCategories: { include: { category: true } },
              entityTags: { include: { tag: true } },
              entityFeatures: { include: { feature: true } },
              // Include all possible details for the response
              entityDetailsTool: true,
              entityDetailsCourse: true,
              entityDetailsAgency: true,
              entityDetailsContentCreator: true,
              entityDetailsCommunity: true,
              entityDetailsNewsletter: true,
              entityDetailsDataset: true,
              entityDetailsResearchPaper: true,
              entityDetailsSoftware: true,
              entityDetailsModel: true,
              entityDetailsProjectReference: true,
              entityDetailsServiceProvider: true,
              entityDetailsInvestor: true,
              entityDetailsEvent: true,
              entityDetailsJob: true,
              entityDetailsGrant: true,
              entityDetailsBounty: true,
              entityDetailsHardware: true,
              entityDetailsNews: true,
              entityDetailsBook: true,
              entityDetailsPodcast: true,
              entityDetailsPlatform: true,
            },
          });

          // Create user submitted tool record
          await tx.userSubmittedTool.create({
            data: {
              userId: submitterUser.id,
              entityId: newEntityTx.id,
              submissionStatus: 'PENDING',
            },
          });

          await this.generateAndSaveEmbedding(newEntityTx.id, tx);
          return newEntityTx;
    });

    // Log the submission activity
    await this.activityLogger.logSubmissionActivity(
      submitterUser.id,
      newEntity.id,
      newEntity.name,
      newEntity.slug || newEntity.id,
      'submitted'
    );

    return newEntity;
  }

  async findAll(
    listEntitiesDto: ListEntitiesDto,
  ): Promise<PaginatedResponse<Entity>> {
    const {
      page = 1,
      limit = 10,
      status,
      entityTypeIds,
      categoryIds,
      tagIds,
      featureIds,
      searchTerm,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      submitterId,
      createdAtFrom,
      createdAtTo,
      hasFreeTier,
      employeeCountRanges,
      fundingStages,
      locationSearch,
      apiAccess,
      pricingModels,
      priceRanges,
      integrations,
      platforms,
      targetAudience,
      rating_min,
      rating_max,
      review_count_min,
      review_count_max,
      affiliate_status,
      has_affiliate_link,
      // Tool/AI Tool filters
      technical_levels,
      learning_curves,
      has_api,
      has_free_tier,
      open_source,
      mobile_support,
      demo_available,
      frameworks,
      libraries,
      key_features_search,
      use_cases_search,
      target_audience_search,
      deployment_options,
      support_channels,
      has_live_chat,
      customization_level,
      pricing_details_search,
      // Course filters
      skill_levels,
      certificate_available,
      instructor_name,
      duration_text,
      enrollment_min,
      enrollment_max,
      prerequisites,
      has_syllabus,
      // Job filters
      employment_types,
      experience_levels,
      location_types,
      company_name,
      job_title,
      salary_min,
      salary_max,
      job_description,
      has_application_url,
      // Hardware filters
      hardware_types,
      manufacturers,
      release_date_from,
      release_date_to,
      price_range,
      price_min,
      price_max,
      specifications_search,
      has_datasheet,
      memory_search,
      processor_search,
      // Event filters
      event_types,
      start_date_from,
      start_date_to,
      end_date_from,
      end_date_to,
      is_online,
      location,
      price_text,
      registration_required,
      has_registration_url,
      speakers_search,
      // Agency filters
      services_offered,
      industry_focus,
      has_portfolio,
      // Software filters
      license_types,
      programming_languages,
      platform_compatibility,
      has_repository,
      current_version,
      // Research Paper filters
      research_areas,
      authors_search,
      publication_date_from,
      publication_date_to,
      // Book filters
      author_name,
      isbn,
      formats,
    } = listEntitiesDto;

    const skip = (page - 1) * limit;

    const where: Prisma.EntityWhereInput = {};
    const detailFilters: Prisma.EntityWhereInput[] = [];

    // Basic top-level filters
    if (status) where.status = status;
    if (entityTypeIds?.length) where.entityTypeId = { in: entityTypeIds };
    if (submitterId) where.submitterId = submitterId;
    if (employeeCountRanges?.length) where.employeeCountRange = { in: employeeCountRanges };
    if (fundingStages?.length) where.fundingStage = { in: fundingStages };
    if (locationSearch) where.locationSummary = { contains: locationSearch, mode: 'insensitive' };
    if (affiliate_status) where.affiliateStatus = affiliate_status as any;
    if (has_affiliate_link !== undefined) {
      if (has_affiliate_link) {
        where.refLink = { not: null };
      } else {
        where.refLink = null;
      }
    }

    if (createdAtFrom || createdAtTo) {
      const createdAtCondition: Prisma.DateTimeFilter = {};
      if (createdAtFrom) createdAtCondition.gte = createdAtFrom;
      if (createdAtTo) createdAtCondition.lte = createdAtTo;
      where.createdAt = createdAtCondition;
    }

    // Relational filters (many-to-many)
    if (categoryIds?.length) where.entityCategories = { some: { categoryId: { in: categoryIds } } };
    if (tagIds?.length) where.entityTags = { some: { tagId: { in: tagIds } } };
    if (featureIds?.length) where.entityFeatures = { some: { featureId: { in: featureIds } } };

    // Detail-level filters (requires OR across different detail tables)
    if (hasFreeTier !== undefined) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { hasFreeTier: hasFreeTier } },
          { entityDetailsSoftware: { hasFreeTier: hasFreeTier } },
          { entityDetailsPlatform: { hasFreeTier: hasFreeTier } },
        ],
      });
    }

    if (apiAccess !== undefined) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { apiAccess: apiAccess } },
          { entityDetailsSoftware: { apiAccess: apiAccess } as any },
          { entityDetailsPlatform: { apiAccess: apiAccess } as any },
        ],
      });
    }

    if (pricingModels && pricingModels.length > 0) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { pricingModel: { in: pricingModels } } },
          { entityDetailsSoftware: { pricingModel: { in: pricingModels } } },
          { entityDetailsPlatform: { pricingModel: { in: pricingModels } } },
        ],
      });
    }

    if (priceRanges && priceRanges.length > 0) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { priceRange: { in: priceRanges } } },
          { entityDetailsSoftware: { priceRange: { in: priceRanges } } },
          { entityDetailsPlatform: { priceRange: { in: priceRanges } } },
        ],
      });
    }
    
    if (integrations && integrations.length > 0) {
      detailFilters.push({
        OR: [
          // For EntityDetailsTool: integrations is Json, use array_contains for JSON arrays
          { entityDetailsTool: { integrations: { array_contains: integrations } } },
          // For EntityDetailsSoftware: integrations is String[], use hasSome
          { entityDetailsSoftware: { integrations: { hasSome: integrations } } },
          // For EntityDetailsPlatform: integrations is String[], use hasSome
          { entityDetailsPlatform: { integrations: { hasSome: integrations } } },
        ],
      });
    }

    if (platforms && platforms.length > 0) {
      detailFilters.push({
        OR: [
          // For EntityDetailsTool: platforms is Json, use array_contains for JSON arrays
          { entityDetailsTool: { platforms: { array_contains: platforms } } },
          // For EntityDetailsSoftware: supportedOs is String[], use hasSome
          { entityDetailsSoftware: { supportedOs: { hasSome: platforms } } },
          // For EntityDetailsPlatform: supportedOs is String[], use hasSome
          { entityDetailsPlatform: { supportedOs: { hasSome: platforms } } },
        ],
      });
    }

    if (targetAudience && targetAudience.length > 0) {
      detailFilters.push({
        OR: [
          // For EntityDetailsTool: targetAudience is Json, use array_contains for JSON arrays
          { entityDetailsTool: { targetAudience: { array_contains: targetAudience } } },
          // For EntityDetailsSoftware: targetAudience is String[], use hasSome
          { entityDetailsSoftware: { targetAudience: { hasSome: targetAudience } } },
          // For EntityDetailsPlatform: targetAudience is String[], use hasSome
          { entityDetailsPlatform: { targetAudience: { hasSome: targetAudience } } },
        ],
      });
    }

    // Rating and Review Count Filters
    if (rating_min !== undefined || rating_max !== undefined) {
      // Calculate average rating from approved reviews
      const ratingCondition: any = {};
      if (rating_min !== undefined) ratingCondition.gte = rating_min;
      if (rating_max !== undefined) ratingCondition.lte = rating_max;

      detailFilters.push({
        reviews: {
          some: {
            status: 'APPROVED',
            rating: ratingCondition
          }
        }
      });
    }

    if (review_count_min !== undefined || review_count_max !== undefined) {
      // This requires a more complex query - we'll use a having clause approach
      const reviewCountCondition: any = {};
      if (review_count_min !== undefined) reviewCountCondition.gte = review_count_min;
      if (review_count_max !== undefined) reviewCountCondition.lte = review_count_max;

      // For now, we'll use a simpler approach with some/none
      if (review_count_min !== undefined && review_count_min > 0) {
        detailFilters.push({
          reviews: {
            some: {
              status: 'APPROVED'
            }
          }
        });
      }
    }

    // Enhanced Entity Type Specific Filters (Flat Parameters)

    // Tool/AI Tool filters
    if (technical_levels?.length) {
      detailFilters.push({
        entityDetailsTool: {
          technicalLevel: { in: technical_levels }
        }
      });
    }

    if (learning_curves?.length) {
      detailFilters.push({
        entityDetailsTool: {
          learningCurve: { in: learning_curves }
        }
      });
    }

    if (has_api !== undefined) {
      detailFilters.push({
        entityDetailsTool: {
          apiAccess: has_api
        }
      });
    }

    if (has_free_tier !== undefined) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { hasFreeTier: has_free_tier } },
          { entityDetailsSoftware: { hasFreeTier: has_free_tier } },
          { entityDetailsPlatform: { hasFreeTier: has_free_tier } },
        ]
      });
    }

    if (open_source !== undefined) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { openSource: open_source } },
          { entityDetailsSoftware: { openSource: open_source } },
        ]
      });
    }

    if (mobile_support !== undefined) {
      detailFilters.push({
        entityDetailsTool: {
          mobileSupport: mobile_support
        }
      });
    }

    if (demo_available !== undefined) {
      detailFilters.push({
        entityDetailsTool: {
          demoAvailable: demo_available
        }
      });
    }

    if (frameworks?.length) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { frameworks: { array_contains: frameworks } } },
          { entityDetailsSoftware: { frameworks: { hasSome: frameworks } } },
        ]
      });
    }

    if (libraries?.length) {
      detailFilters.push({
        entityDetailsTool: {
          libraries: { array_contains: libraries }
        }
      });
    }

    if (key_features_search) {
      detailFilters.push({
        entityDetailsTool: {
          keyFeatures: { array_contains: [key_features_search] }
        }
      });
    }

    if (use_cases_search) {
      detailFilters.push({
        entityDetailsTool: {
          useCases: { array_contains: [use_cases_search] }
        }
      });
    }

    if (target_audience_search) {
      detailFilters.push({
        OR: [
          { entityDetailsTool: { targetAudience: { array_contains: [target_audience_search] } } },
          { entityDetailsSoftware: { targetAudience: { hasSome: [target_audience_search] } } },
          { entityDetailsPlatform: { targetAudience: { hasSome: [target_audience_search] } } },
        ]
      });
    }

    if (deployment_options?.length) {
      detailFilters.push({
        entityDetailsTool: {
          deploymentOptions: { array_contains: deployment_options }
        }
      });
    }

    if (support_channels?.length) {
      detailFilters.push({
        entityDetailsTool: {
          supportChannels: { array_contains: support_channels }
        }
      });
    }

    if (has_live_chat !== undefined) {
      detailFilters.push({
        entityDetailsTool: {
          hasLiveChat: has_live_chat
        }
      });
    }

    if (customization_level) {
      detailFilters.push({
        entityDetailsTool: {
          customizationLevel: { contains: customization_level, mode: 'insensitive' }
        }
      });
    }

    if (pricing_details_search) {
      detailFilters.push({
        entityDetailsTool: {
          pricingDetails: { contains: pricing_details_search, mode: 'insensitive' }
        }
      });
    }

    // Course filters
    if (skill_levels?.length) {
      detailFilters.push({
        entityDetailsCourse: {
          skillLevel: { in: skill_levels }
        }
      });
    }

    if (certificate_available !== undefined) {
      detailFilters.push({
        entityDetailsCourse: {
          certificateAvailable: certificate_available
        }
      });
    }

    if (instructor_name) {
      detailFilters.push({
        entityDetailsCourse: {
          instructorName: { contains: instructor_name, mode: 'insensitive' }
        }
      });
    }

    if (duration_text) {
      detailFilters.push({
        entityDetailsCourse: {
          durationText: { contains: duration_text, mode: 'insensitive' }
        }
      });
    }

    if (enrollment_min !== undefined || enrollment_max !== undefined) {
      const enrollmentCondition: any = {};
      if (enrollment_min !== undefined) enrollmentCondition.gte = enrollment_min;
      if (enrollment_max !== undefined) enrollmentCondition.lte = enrollment_max;

      detailFilters.push({
        entityDetailsCourse: {
          enrollmentCount: enrollmentCondition
        }
      });
    }

    if (prerequisites) {
      detailFilters.push({
        entityDetailsCourse: {
          prerequisites: { contains: prerequisites, mode: 'insensitive' }
        }
      });
    }

    if (has_syllabus !== undefined) {
      detailFilters.push({
        entityDetailsCourse: {
          syllabusUrl: has_syllabus ? { not: null } : null
        }
      });
    }

    // Job filters
    if (employment_types?.length) {
      detailFilters.push({
        entityDetailsJob: {
          employmentTypes: { hasSome: employment_types }
        }
      });
    }

    if (experience_levels?.length) {
      // experienceLevel is a single enum field, so we need to use OR conditions for multiple values
      const experienceLevelConditions = experience_levels.map((level: string) => ({
        entityDetailsJob: {
          experienceLevel: level as any
        }
      }));
      detailFilters.push({ OR: experienceLevelConditions } as any);
    }

    if (location_types?.length) {
      const locationConditions = location_types.map((locType: string) => ({
        entityDetailsJob: {
          location: { contains: locType, mode: 'insensitive' }
        }
      }));
      detailFilters.push({ OR: locationConditions } as any);
    }

    if (company_name) {
      detailFilters.push({
        entityDetailsJob: {
          companyName: { contains: company_name, mode: 'insensitive' }
        }
      });
    }

    if (job_title) {
      detailFilters.push({
        entityDetailsJob: {
          jobType: { contains: job_title, mode: 'insensitive' }
        }
      });
    }

    if (salary_min !== undefined || salary_max !== undefined) {
      const salaryCondition: any = {};
      if (salary_min !== undefined) salaryCondition.gte = salary_min;
      if (salary_max !== undefined) salaryCondition.lte = salary_max;

      detailFilters.push({
        entityDetailsJob: {
          salaryMin: salaryCondition
        }
      });
    }

    if (job_description) {
      detailFilters.push({
        entityDetailsJob: {
          jobDescription: { contains: job_description, mode: 'insensitive' }
        }
      });
    }

    if (has_application_url !== undefined) {
      detailFilters.push({
        entityDetailsJob: {
          applicationUrl: has_application_url ? { not: null } : null
        }
      });
    }

    // Hardware filters - now using all available fields from enhanced schema
    if (hardware_types?.length) {
      detailFilters.push({
        entityDetailsHardware: {
          hardwareType: { in: hardware_types }
        }
      });
    }

    if (manufacturers?.length) {
      detailFilters.push({
        entityDetailsHardware: {
          manufacturer: { in: manufacturers }
        }
      });
    }

    if (release_date_from || release_date_to) {
      const dateCondition: any = {};
      if (release_date_from) dateCondition.gte = new Date(release_date_from);
      if (release_date_to) dateCondition.lte = new Date(release_date_to);

      detailFilters.push({
        entityDetailsHardware: {
          releaseDate: dateCondition
        }
      });
    }

    if (price_range) {
      detailFilters.push({
        entityDetailsHardware: {
          price: { contains: price_range, mode: 'insensitive' }
        }
      });
    }

    if (price_min !== undefined || price_max !== undefined) {
      // Since price is a string field, we'll search for the price range in the price text
      const priceSearchTerms = [];
      if (price_min !== undefined) priceSearchTerms.push(price_min.toString());
      if (price_max !== undefined) priceSearchTerms.push(price_max.toString());

      detailFilters.push({
        entityDetailsHardware: {
          price: { contains: priceSearchTerms.join('-'), mode: 'insensitive' }
        }
      });
    }

    // Temporarily disable specifications_search to fix server crash
    // TODO: Implement proper JSON field search syntax
    // if (specifications_search) {
    //   detailFilters.push({
    //     entityDetailsHardware: {
    //       specifications: {
    //         string_contains: specifications_search
    //       }
    //     }
    //   });
    // }

    if (has_datasheet !== undefined) {
      detailFilters.push({
        entityDetailsHardware: {
          datasheetUrl: has_datasheet ? { not: null } : null
        }
      });
    }

    if (memory_search) {
      detailFilters.push({
        entityDetailsHardware: {
          memory: { contains: memory_search, mode: 'insensitive' }
        }
      });
    }

    if (processor_search) {
      detailFilters.push({
        entityDetailsHardware: {
          processor: { contains: processor_search, mode: 'insensitive' }
        }
      });
    }

    // Event filters
    if (event_types?.length) {
      detailFilters.push({
        entityDetailsEvent: {
          eventType: { in: event_types }
        }
      });
    }

    if (start_date_from || start_date_to) {
      const startDateCondition: any = {};
      if (start_date_from) startDateCondition.gte = new Date(start_date_from);
      if (start_date_to) startDateCondition.lte = new Date(start_date_to);

      detailFilters.push({
        entityDetailsEvent: {
          startDate: startDateCondition
        }
      });
    }

    if (end_date_from || end_date_to) {
      const endDateCondition: any = {};
      if (end_date_from) endDateCondition.gte = new Date(end_date_from);
      if (end_date_to) endDateCondition.lte = new Date(end_date_to);

      detailFilters.push({
        entityDetailsEvent: {
          endDate: endDateCondition
        }
      });
    }

    if (is_online !== undefined) {
      detailFilters.push({
        entityDetailsEvent: {
          isOnline: is_online
        }
      });
    }

    if (location) {
      detailFilters.push({
        entityDetailsEvent: {
          location: { contains: location, mode: 'insensitive' }
        }
      });
    }

    if (price_text) {
      detailFilters.push({
        entityDetailsEvent: {
          price: { contains: price_text, mode: 'insensitive' }
        }
      });
    }

    if (registration_required !== undefined) {
      detailFilters.push({
        entityDetailsEvent: {
          registrationRequired: registration_required
        }
      });
    }

    if (has_registration_url !== undefined) {
      detailFilters.push({
        entityDetailsEvent: {
          registrationUrl: has_registration_url ? { not: null } : null
        }
      });
    }

    if (speakers_search) {
      detailFilters.push({
        entityDetailsEvent: {
          keySpeakers: { has: speakers_search }
        }
      });
    }

    // Agency filters
    if (services_offered?.length) {
      detailFilters.push({
        entityDetailsAgency: {
          servicesOffered: { array_contains: services_offered } as any
        }
      });
    }

    if (industry_focus?.length) {
      detailFilters.push({
        entityDetailsAgency: {
          industryFocus: { array_contains: industry_focus } as any
        }
      });
    }

    if (has_portfolio !== undefined) {
      detailFilters.push({
        entityDetailsAgency: {
          portfolioUrl: has_portfolio ? { not: null } : null
        }
      });
    }

    // Software filters
    if (license_types?.length) {
      detailFilters.push({
        entityDetailsSoftware: {
          licenseType: { in: license_types }
        }
      });
    }

    if (programming_languages?.length) {
      detailFilters.push({
        entityDetailsSoftware: {
          programmingLanguages: { hasSome: programming_languages }
        }
      });
    }

    if (platform_compatibility?.length) {
      detailFilters.push({
        entityDetailsSoftware: {
          platformCompatibility: { hasSome: platform_compatibility }
        }
      });
    }

    if (has_repository !== undefined) {
      detailFilters.push({
        entityDetailsSoftware: {
          repositoryUrl: has_repository ? { not: null } : null
        }
      });
    }

    if (current_version) {
      detailFilters.push({
        entityDetailsSoftware: {
          currentVersion: { contains: current_version, mode: 'insensitive' }
        }
      });
    }

    // Research Paper filters - now using enhanced schema fields
    if (research_areas?.length) {
      detailFilters.push({
        entityDetailsResearchPaper: {
          researchAreas: { hasSome: research_areas }
        }
      });
    }

    if (authors_search) {
      detailFilters.push({
        entityDetailsResearchPaper: {
          authors: { has: authors_search }
        }
      });
    }

    if (publication_date_from || publication_date_to) {
      const dateCondition: any = {};
      if (publication_date_from) dateCondition.gte = new Date(publication_date_from);
      if (publication_date_to) dateCondition.lte = new Date(publication_date_to);

      detailFilters.push({
        entityDetailsResearchPaper: {
          publicationDate: dateCondition
        }
      });
    }

    // Book filters
    if (author_name) {
      detailFilters.push({
        entityDetailsBook: {
          author: { contains: author_name, mode: 'insensitive' }
        }
      });
    }

    if (isbn) {
      detailFilters.push({
        entityDetailsBook: {
          isbn: { contains: isbn, mode: 'insensitive' }
        }
      });
    }

    if (formats?.length) {
      // Since format is a single string field, we'll check if any of the requested formats match
      const formatConditions = formats.map((fmt: string) => ({
        entityDetailsBook: {
          format: { contains: fmt, mode: 'insensitive' }
        }
      }));
      detailFilters.push({ OR: formatConditions } as any);
    }

    if (detailFilters.length > 0) {
        where.AND = [...(where.AND as any[] || []), ...detailFilters];
    }
    
    if (searchTerm) {
      // For now, let's use a simple ILIKE search instead of full-text search to fix the validation issue
      // We can implement proper FTS later
      const searchPattern = `%${searchTerm}%`;

      const searchWhere = {
        ...where,
        OR: [
          { name: { contains: searchTerm, mode: 'insensitive' } },
          { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
          { description: { contains: searchTerm, mode: 'insensitive' } },
        ],
      } as Prisma.EntityWhereInput;

      const total = await this.prisma.entity.count({ where: searchWhere });
      const entities = await this.prisma.entity.findMany({
        where: searchWhere,
        include: this.getFindAllIncludes(),
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });

      return {
        data: entities,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }
    
    const total = await this.prisma.entity.count({ where });

    // Build advanced sorting
    const orderBy = this.buildOrderBy(sortBy, sortOrder);

    const entities = await this.prisma.entity.findMany({
      where,
      include: this.getFindAllIncludes(),
      orderBy,
      skip,
      take: limit,
    });

    return {
      data: entities,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  private getFindAllIncludes(): Prisma.EntityInclude {
    return {
      entityType: true,
      submitter: {
        select: {
          id: true,
          authUserId: true,
          email: true,
          createdAt: true,
          lastLogin: true,
          username: true,
          displayName: true,
          profilePictureUrl: true,
        },
      },
      entityCategories: { include: { category: true } },
      entityTags: { include: { tag: true } },
      entityFeatures: { include: { feature: true } },
      reviews: {
        where: { status: ReviewStatus.APPROVED },
        select: { rating: true },
      },
      entityDetailsTool: true,
      entityDetailsCourse: true,
      entityDetailsAgency: true,
      entityDetailsContentCreator: true,
      entityDetailsCommunity: true,
      entityDetailsNewsletter: true,
      entityDetailsDataset: true,
      entityDetailsResearchPaper: true,
      entityDetailsSoftware: true,
      entityDetailsModel: true,
      entityDetailsProjectReference: true,
      entityDetailsServiceProvider: true,
      entityDetailsInvestor: true,
      entityDetailsEvent: true,
      entityDetailsJob: true,
      entityDetailsGrant: true,
      entityDetailsBounty: true,
      entityDetailsHardware: true,
      entityDetailsNews: true,
      entityDetailsBook: true,
      entityDetailsPodcast: true,
      entityDetailsPlatform: true,
      _count: {
        select: {
          userSavedEntities: true,
        },
      },
    };
  }

  async findOne(id: string): Promise<Entity | null> {
    const entity = await this.prisma.entity.findUnique({
      where: { id },
      include: {
        entityType: true,
        submitter: { 
          select: { 
            id: true, 
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true 
          }
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityFeatures: { include: { feature: true } },
        reviews: {
          where: { status: ReviewStatus.APPROVED },
          orderBy: { createdAt: 'desc' },
          include: { 
            user: { select: { id: true, username: true, profilePictureUrl: true } },
            reviewVotes: true,
          }
        },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
      },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // No second query needed if all details are included above.
    // The correct detail table will be populated, others will be null.
    return entity;
  }

  async findBySlug(slug: string): Promise<Entity | null> {
    const entity = await this.prisma.entity.findUnique({
      where: { slug },
      include: {
        entityType: true,
        submitter: {
          select: {
            id: true,
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true
          }
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityFeatures: { include: { feature: true } },
        reviews: {
          where: { status: ReviewStatus.APPROVED },
          orderBy: { createdAt: 'desc' },
          include: {
            user: { select: { id: true, username: true, profilePictureUrl: true } },
            reviewVotes: true,
          }
        },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
      },
    });

    return entity;
  }

  async update(id: string, updateEntityDto: UpdateEntityDto, currentUser: UserModel): Promise<Entity> {
    const {
      // Destructure all direct entity fields from DTO
      name,
      website_url,
      // entity_type_id cannot be updated directly, handle via specific admin endpoint if needed
      short_description,
      description,
      logo_url,
      documentation_url,
      contact_url,
      privacy_policy_url,
      founded_year,
      social_links,
      category_ids,
      tag_ids,
      feature_ids,
      status, 

      meta_title,
      meta_description,
      employee_count_range,
      funding_stage,
      location_summary,
      ref_link,
      affiliate_status,
      scraped_review_sentiment_label,
      scraped_review_sentiment_score,
      scraped_review_count,

      tool_details,
      course_details,
      agency_details,
      content_creator_details,
      community_details,
      newsletter_details,
      dataset_details,
      research_paper_details,
      software_details,
      model_details,
      project_reference_details,
      service_provider_details,
      investor_details,
      event_details,
      job_details,
      grant_details,
      bounty_details,
      hardware_details,
      news_details,
      book_details,
      podcast_details,
      platform_details,
    } = updateEntityDto;

    // Fetch the current entity with its relations to check ownership and current state
    const entity = await this.prisma.entity.findUnique({
      where: { id },
      include: {
        submitter: true,
        entityCategories: true,
        entityTags: true,
        entityFeatures: true, // Include existing features for context if needed, though deleteMany/create handles it
        entityType: true, // To get slug for details DTO mapping
      },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // Authorization: Check if the current user is the submitter or an admin/moderator
    const canUpdate = 
      entity.submitterId === currentUser.id || 
      currentUser.role === UserRole.ADMIN || 
      currentUser.role === UserRole.MODERATOR;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this entity.');
    }

    const updateData: Prisma.EntityUpdateInput = {};

    // Direct entity fields that can be updated
    if (name !== undefined) {
      updateData.name = name;
      // Generate new slug if name is being updated
      updateData.slug = await this.getUniqueSlug(name);
    }
    if (website_url !== undefined) updateData.websiteUrl = website_url;
    if (short_description !== undefined) updateData.shortDescription = short_description;
    if (description !== undefined) updateData.description = description;
    if (logo_url !== undefined) updateData.logoUrl = logo_url;
    if (documentation_url !== undefined) updateData.documentationUrl = documentation_url;
    if (contact_url !== undefined) updateData.contactUrl = contact_url;
    if (privacy_policy_url !== undefined) updateData.privacyPolicyUrl = privacy_policy_url;
    if (founded_year !== undefined) updateData.foundedYear = founded_year;
    if (social_links !== undefined) updateData.socialLinks = social_links as Prisma.InputJsonValue | undefined;
    
    if (meta_title !== undefined) updateData.metaTitle = meta_title;
    if (meta_description !== undefined) updateData.metaDescription = meta_description;
    if (employee_count_range !== undefined) updateData.employeeCountRange = employee_count_range;
    if (funding_stage !== undefined) updateData.fundingStage = funding_stage;
    if (location_summary !== undefined) updateData.locationSummary = location_summary;
    if (ref_link !== undefined) updateData.refLink = ref_link;
    if (affiliate_status !== undefined) updateData.affiliateStatus = affiliate_status;
    if (scraped_review_sentiment_label !== undefined) updateData.scrapedReviewSentimentLabel = scraped_review_sentiment_label;
    if (scraped_review_sentiment_score !== undefined) updateData.scrapedReviewSentimentScore = scraped_review_sentiment_score;
    if (scraped_review_count !== undefined) updateData.scrapedReviewCount = scraped_review_count;

    // Handle status updates - ensuring only admin/moderator can set to certain statuses
    if (status !== undefined) {
      if (
        currentUser.role === UserRole.ADMIN ||
        currentUser.role === UserRole.MODERATOR
      ) {
        updateData.status = status;
      } else if (status === EntityStatus.PENDING && entity.status === EntityStatus.NEEDS_REVISION) {
        // User resubmitting after 'NEEDS_REVISION'
        updateData.status = EntityStatus.PENDING;
      } else if (status !== entity.status) {
        this.logger.warn(
          `User ${currentUser.id} attempted to change status of entity ${id} from ${entity.status} to ${status} without ADMIN/MODERATOR role.`,
        );
      }
    }

    // Handle category_ids update: disconnect all, then connect new ones
    if (category_ids !== undefined) {
      updateData.entityCategories = {
        deleteMany: {},
        create: category_ids.map(catId => ({ categoryId: catId, assignedBy: currentUser.id })),
      };
    }

    // Handle tag_ids update: disconnect all, then connect new ones
    if (tag_ids !== undefined) {
      updateData.entityTags = {
        deleteMany: {},
        create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: currentUser.id })),
      };
    }

    // Handle feature_ids update: disconnect all, then connect new ones
    if (feature_ids !== undefined) {
      updateData.entityFeatures = {
        deleteMany: {},
        create: feature_ids.map(featId => ({ featureId: featId, assignedBy: currentUser.id })),
      };
    }

        // Type-specific details update logic
        const entityTypeSlug = entity.entityType.slug;
        if (entityTypeSlug === 'ai-tool' && tool_details) {
          const detailDataForPrisma = this.mapToolDetailsToPrisma(tool_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsTool = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsToolUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsToolUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'online-course' && course_details) {
          const detailDataForPrisma = this.mapCourseDetailsToPrisma(course_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsCourse = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsCourseUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsCourseUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'agency' && agency_details) {
          const detailDataForPrisma = this.mapAgencyDetailsToPrisma(agency_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsAgency = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsAgencyUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsAgencyUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'content-creator' && content_creator_details) {
          const detailDataForPrisma = this.mapContentCreatorDetailsToPrisma(content_creator_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsContentCreator = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsContentCreatorUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsContentCreatorUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'community' && community_details) {
          const detailDataForPrisma = this.mapCommunityDetailsToPrisma(community_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsCommunity = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsCommunityUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsCommunityUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'newsletter' && newsletter_details) {
          const detailDataForPrisma = this.mapNewsletterDetailsToPrisma(newsletter_details);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsNewsletter = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsNewsletterUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsNewsletterUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'dataset' && dataset_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(dataset_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsDataset = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsDatasetUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsDatasetUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'research-paper' && research_paper_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(research_paper_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsResearchPaper = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsResearchPaperUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsResearchPaperUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'software' && software_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(software_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsSoftware = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsSoftwareUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsSoftwareUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'model' && model_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(model_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsModel = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsModelUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsModelUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'project-reference' && project_reference_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(project_reference_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsProjectReference = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsProjectReferenceUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsProjectReferenceUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'service-provider' && service_provider_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(service_provider_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsServiceProvider = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsServiceProviderUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsServiceProviderUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'investor' && investor_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(investor_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsInvestor = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsInvestorUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsInvestorUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'event' && event_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(event_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsEvent = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsEventUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsEventUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'job' && job_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(job_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsJob = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsJobUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsJobUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'grant' && grant_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(grant_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsGrant = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsGrantUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsGrantUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'bounty' && bounty_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(bounty_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsBounty = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsBountyUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsBountyUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'hardware' && hardware_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(hardware_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsHardware = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsHardwareUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsHardwareUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'news' && news_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(news_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsNews = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsNewsUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsNewsUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'book' && book_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(book_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsBook = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsBookUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsBookUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'podcast' && podcast_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(podcast_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsPodcast = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsPodcastUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsPodcastUpdateInput,
              },
            };
          }
        } else if (entityTypeSlug === 'platform' && platform_details) {
          const detailDataForPrisma = this.mapDetailsToPrisma(platform_details, entityTypeSlug);
          if (Object.keys(detailDataForPrisma).length > 0) {
            updateData.entityDetailsPlatform = {
              upsert: {
                create: detailDataForPrisma as Omit<Prisma.EntityDetailsPlatformUncheckedCreateInput, 'entityId'>,
                update: detailDataForPrisma as Prisma.EntityDetailsPlatformUpdateInput,
              },
            };
          }
        }

    // Step 1: Update the entity and its core data within a transaction
    const updatedEntity = await this.prisma.$transaction(async tx => {
      const updatedEntityTx = await tx.entity.update({
          where: { id },
          data: updateData,
          include: {
            entityType: true,
            submitter: { 
              select: { 
                id: true, 
                authUserId: true,
                email: true,
                createdAt: true,
                lastLogin: true,
                username: true,
                displayName: true,
              profilePictureUrl: true,
            },
            },
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
            entityDetailsTool: true,
            entityDetailsCourse: true,
            entityDetailsAgency: true,
            entityDetailsContentCreator: true,
            entityDetailsCommunity: true,
            entityDetailsNewsletter: true,
            entityDetailsDataset: true,
            entityDetailsResearchPaper: true,
            entityDetailsSoftware: true,
            entityDetailsModel: true,
            entityDetailsProjectReference: true,
            entityDetailsServiceProvider: true,
            entityDetailsInvestor: true,
            entityDetailsEvent: true,
            entityDetailsJob: true,
            entityDetailsGrant: true,
            entityDetailsBounty: true,
            entityDetailsHardware: true,
            entityDetailsNews: true,
            entityDetailsBook: true,
            entityDetailsPodcast: true,
            entityDetailsPlatform: true,
          },
        });
      await this.generateAndSaveEmbedding(updatedEntityTx.id, tx);
      return updatedEntityTx;
    });

    // Refetch to get the latest state and exclude the vector from the response
    const entityForResponse = await this.prisma.entity.findUniqueOrThrow({
      where: { id: updatedEntity.id },
      include: {
        entityType: true,
        submitter: {
          select: {
            id: true,
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true,
          },
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityFeatures: { include: { feature: true } },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
        entityDetailsDataset: true,
        entityDetailsResearchPaper: true,
        entityDetailsSoftware: true,
        entityDetailsModel: true,
        entityDetailsProjectReference: true,
        entityDetailsServiceProvider: true,
        entityDetailsInvestor: true,
        entityDetailsEvent: true,
        entityDetailsJob: true,
        entityDetailsGrant: true,
        entityDetailsBounty: true,
        entityDetailsHardware: true,
        entityDetailsNews: true,
        entityDetailsBook: true,
        entityDetailsPodcast: true,
        entityDetailsPlatform: true,
      },
    });

    // @ts-ignore - a vectorEmbedding property is not expected on the entity
    delete entityForResponse.vector_embedding;

    return entityForResponse as Entity;
  }

  async adminSetStatus(id: string, newStatus: EntityStatus): Promise<Entity> {
    const entityExists = await this.prisma.entity.findUnique({ 
      where: { id },
      // Minimal include to check existence, actual includes for response are done in update
    });
    if (!entityExists) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // Potentially add more specific business logic/validation for admin status changes here
    // For example, if certain statuses can only transition from/to specific other statuses.
    // Or if changing status requires other fields to be updated/nulled.

    this.logger.log(`[EntitiesService AdminSetStatus] Admin changing status of entity ${id} to ${newStatus}`);

    return this.prisma.entity.update({
      where: { id },
      data: { 
        status: newStatus, 
      },
      include: { // Ensure this include matches what your EntityResponseDto expects
        entityType: true,
        submitter: { 
          select: { 
            id: true, 
            authUserId: true,
            email: true,
            createdAt: true,
            lastLogin: true,
            username: true,
            displayName: true,
            profilePictureUrl: true 
          }
        },
        entityCategories: { include: { category: true } },
        entityTags: { include: { tag: true } },
        entityDetailsTool: true,
        entityDetailsCourse: true,
        entityDetailsAgency: true,
        entityDetailsContentCreator: true,
        entityDetailsCommunity: true,
        entityDetailsNewsletter: true,
      },
    });
  }

  async remove(id: string, user: UserModel): Promise<void> {
    // Implementation to be added
    this.logger.log(`[EntitiesService] User ${user.id} requested to remove entity ${id}`);
    const entity = await this.prisma.entity.findUnique({
      where: { id },
    });

    if (!entity) {
      throw new NotFoundException(`Entity with ID "${id}" not found`);
    }

    // Authorization: Check if the current user is the submitter or an admin/moderator
    const canDelete = 
      entity.submitterId === user.id || 
      user.role === UserRole.ADMIN || 
      user.role === UserRole.MODERATOR;

    if (!canDelete) {
      throw new ForbiddenException('You do not have permission to delete this entity.');
    }

    await this.prisma.entity.delete({ where: { id } });
  }

  async vectorSearch(vectorSearchDto: VectorSearchDto): Promise<VectorSearchResult[]> {
    const { query, limit } = vectorSearchDto;
    const match_threshold = 0.5; // Default threshold

    const embedding = await this.openaiService.generateEmbedding(query);
    if (!embedding) {
      this.logger.warn(`Could not generate embedding for query: "${query}"`);
      return [];
    }

    const vectorString = `[${embedding.join(',')}]`;

    try {
      const results = await this.prisma.$queryRaw<VectorSearchResult[]>`
        SELECT
          id,
          name,
          "short_description" as "shortDescription",
          "logo_url" as "logoUrl",
          (
            SELECT slug
            FROM "public"."entity_types"
            WHERE id = "entity_type_id"
          ) as "entityTypeSlug",
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) as similarity
        FROM
          "public"."entities"
        WHERE
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) > ${match_threshold}
        ORDER BY
          similarity DESC
        LIMIT ${limit};
      `;
      return results;
    } catch (error) {
      this.logger.error('Vector search failed', error.stack);
      throw new InternalServerErrorException('An error occurred during vector search.');
    }
  }




  /**
   * Build advanced ordering for entity queries
   */
  private buildOrderBy(sortBy: string, sortOrder: Prisma.SortOrder): any {
    switch (sortBy) {
      case 'averageRating':
        // Sort by denormalized average rating field
        return { avgRating: sortOrder };

      case 'reviewCount':
        // Sort by denormalized review count field
        return { reviewCount: sortOrder };

      case 'saveCount':
        // Sort by number of times entity has been saved
        return {
          userSavedEntities: {
            _count: sortOrder
          }
        };

      case 'popularity':
        // Sort by a combination of saves and reviews (popularity score)
        // For now, we'll use save count as a proxy for popularity
        return {
          userSavedEntities: {
            _count: sortOrder
          }
        };

      case 'relevance':
        // For relevance, we'll fall back to createdAt unless it's a search query
        // (search queries handle relevance differently with _relevance)
        return { createdAt: sortOrder };

      case 'name':
        return { name: sortOrder };

      case 'foundedYear':
        return { foundedYear: sortOrder };

      case 'updatedAt':
        return { updatedAt: sortOrder };

      case 'createdAt':
      default:
        return { createdAt: sortOrder };
    }
  }
}