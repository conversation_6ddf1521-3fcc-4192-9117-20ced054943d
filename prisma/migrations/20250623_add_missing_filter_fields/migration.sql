-- Migration: Add missing fields for comprehensive entity filtering
-- This migration adds fields that the frontend filters expect but are missing from the current schema

-- Add missing hardware fields
ALTER TABLE "public"."entity_details_hardware" 
ADD COLUMN "hardware_type" TEXT,
ADD COLUMN "manufacturer" TEXT,
ADD COLUMN "release_date" DATE,
ADD COLUMN "specifications" JSONB,
ADD COLUMN "datasheet_url" TEXT;

-- Add missing job fields
ALTER TABLE "public"."entity_details_job"
ADD COLUMN "job_description" TEXT,
ADD COLUMN "employment_types" TEXT[],
ADD COLUMN "location_types" TEXT[],
ADD COLUMN "benefits" TEXT[],
ADD COLUMN "remote_policy" TEXT,
ADD COLUMN "visa_sponsorship" BOOLEAN DEFAULT FALSE;

-- Add missing event fields
ALTER TABLE "public"."entity_details_event"
ADD COLUMN "registration_required" BOOLEAN DEFAULT FALSE,
ADD COLUMN "capacity" INTEGER,
ADD COLUMN "organizer" TEXT,
ADD COLUMN "event_format" TEXT; -- 'in-person', 'virtual', 'hybrid'

-- Add missing research paper fields
ALTER TABLE "public"."entity_details_research_paper"
ADD COLUMN "research_areas" TEXT[],
ADD COLUMN "publication_venues" TEXT[],
ADD COLUMN "keywords" TEXT[],
ADD COLUMN "arxiv_id" TEXT;

-- Add missing software fields (restore previously removed fields)
ALTER TABLE "public"."entity_details_software"
ADD COLUMN "repository_url" TEXT,
ADD COLUMN "release_date" DATE;

-- Create hardware type enum for better data consistency
CREATE TYPE "public"."hardware_type_enum" AS ENUM ('GPU', 'CPU', 'FPGA', 'TPU', 'ASIC', 'NPU', 'Memory', 'Storage');

-- Create employment type enum
CREATE TYPE "public"."employment_type_enum" AS ENUM ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE', 'INTERNSHIP', 'TEMPORARY');

-- Create experience level enum
CREATE TYPE "public"."experience_level_enum" AS ENUM ('ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'LEAD', 'PRINCIPAL', 'DIRECTOR');

-- Create location type enum
CREATE TYPE "public"."location_type_enum" AS ENUM ('Remote', 'On-site', 'Hybrid');

-- Create event format enum
CREATE TYPE "public"."event_format_enum" AS ENUM ('in-person', 'virtual', 'hybrid');

-- Add indexes for better query performance
CREATE INDEX "idx_hardware_type" ON "public"."entity_details_hardware"("hardware_type");
CREATE INDEX "idx_hardware_manufacturer" ON "public"."entity_details_hardware"("manufacturer");
CREATE INDEX "idx_hardware_release_date" ON "public"."entity_details_hardware"("release_date");

CREATE INDEX "idx_job_employment_types" ON "public"."entity_details_job" USING GIN("employment_types");
CREATE INDEX "idx_job_location_types" ON "public"."entity_details_job" USING GIN("location_types");
CREATE INDEX "idx_job_salary_range" ON "public"."entity_details_job"("salary_min", "salary_max");

CREATE INDEX "idx_event_registration_required" ON "public"."entity_details_event"("registration_required");
CREATE INDEX "idx_event_format" ON "public"."entity_details_event"("event_format");
CREATE INDEX "idx_event_date_range" ON "public"."entity_details_event"("start_date", "end_date");

CREATE INDEX "idx_research_areas" ON "public"."entity_details_research_paper" USING GIN("research_areas");
CREATE INDEX "idx_research_keywords" ON "public"."entity_details_research_paper" USING GIN("keywords");
CREATE INDEX "idx_research_publication_date" ON "public"."entity_details_research_paper"("publication_date");

CREATE INDEX "idx_software_repository" ON "public"."entity_details_software"("repository_url");
CREATE INDEX "idx_software_release_date" ON "public"."entity_details_software"("release_date");

-- Add full-text search indexes for better search performance
CREATE INDEX "idx_hardware_specifications_search" ON "public"."entity_details_hardware" USING GIN(to_tsvector('english', COALESCE(specifications::text, '')));
CREATE INDEX "idx_job_description_search" ON "public"."entity_details_job" USING GIN(to_tsvector('english', COALESCE(job_description, '')));
CREATE INDEX "idx_research_abstract_search" ON "public"."entity_details_research_paper" USING GIN(to_tsvector('english', COALESCE(abstract, '')));
