-- Performance Optimization Migration
-- This migration adds comprehensive indexes for optimal query performance

-- ========================================
-- ENTITY TABLE INDEXES
-- ========================================

-- Core entity filtering indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_status_type" ON "public"."entities"("status", "entity_type_id");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_created_rating" ON "public"."entities"("created_at" DESC, "avg_rating" DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_rating_reviews" ON "public"."entities"("avg_rating" DESC, "review_count" DESC);

-- Full-text search index for entities
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_search" ON "public"."entities" USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- ========================================
-- TOOL ENTITY INDEXES
-- ========================================

-- Boolean filter combinations for tools
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_api_features" ON "public"."entity_details_tool"("api_access", "has_free_tier", "open_source");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_support_features" ON "public"."entity_details_tool"("mobile_support", "demo_available", "has_live_chat");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_technical_level" ON "public"."entity_details_tool"("technical_level");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_learning_curve" ON "public"."entity_details_tool"("learning_curve");

-- Array field indexes for tools
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_frameworks" ON "public"."entity_details_tool" USING gin("frameworks");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_libraries" ON "public"."entity_details_tool" USING gin("libraries");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_deployment_options" ON "public"."entity_details_tool" USING gin("deployment_options");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_support_channels" ON "public"."entity_details_tool" USING gin("support_channels");

-- JSON field indexes for tools
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_target_audience" ON "public"."entity_details_tool" USING gin("target_audience");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_key_features" ON "public"."entity_details_tool" USING gin("key_features");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_use_cases" ON "public"."entity_details_tool" USING gin("use_cases");

-- ========================================
-- COURSE ENTITY INDEXES
-- ========================================

-- Course-specific indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_skill_level" ON "public"."entity_details_course"("skill_level");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_certificate" ON "public"."entity_details_course"("certificate_available");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_enrollment" ON "public"."entity_details_course"("enrollment_count");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_duration" ON "public"."entity_details_course"("duration_weeks");

-- Text search indexes for courses
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_instructor_search" ON "public"."entity_details_course" USING gin(to_tsvector('english', COALESCE("instructor_name", '')));
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_course_prerequisites_search" ON "public"."entity_details_course" USING gin(to_tsvector('english', COALESCE("prerequisites", '')));

-- ========================================
-- JOB ENTITY INDEXES
-- ========================================

-- Job filtering indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_employment_types" ON "public"."entity_details_job" USING gin("employment_types");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_location_types" ON "public"."entity_details_job" USING gin("location_types");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_experience_level" ON "public"."entity_details_job"("experience_level");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_remote" ON "public"."entity_details_job"("is_remote");

-- Salary range indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_salary_range" ON "public"."entity_details_job"("salary_min", "salary_max");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_salary_min" ON "public"."entity_details_job"("salary_min") WHERE "salary_min" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_salary_max" ON "public"."entity_details_job"("salary_max") WHERE "salary_max" IS NOT NULL;

-- Text search indexes for jobs
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_company_search" ON "public"."entity_details_job" USING gin(to_tsvector('english', COALESCE("company_name", '')));
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_description_search" ON "public"."entity_details_job" USING gin(to_tsvector('english', COALESCE("job_description", '')));

-- ========================================
-- HARDWARE ENTITY INDEXES
-- ========================================

-- Hardware type and manufacturer indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_type" ON "public"."entity_details_hardware"("hardware_type");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_manufacturer" ON "public"."entity_details_hardware"("manufacturer");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_type_manufacturer" ON "public"."entity_details_hardware"("hardware_type", "manufacturer");

-- Hardware date and price indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_release_date" ON "public"."entity_details_hardware"("release_date") WHERE "release_date" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_datasheet" ON "public"."entity_details_hardware"("datasheet_url") WHERE "datasheet_url" IS NOT NULL;

-- Hardware specifications search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_specifications_search" ON "public"."entity_details_hardware" USING gin(to_tsvector('english', COALESCE("specifications"::text, '')));
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_memory_search" ON "public"."entity_details_hardware" USING gin(to_tsvector('english', COALESCE("memory", '')));
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_hardware_processor_search" ON "public"."entity_details_hardware" USING gin(to_tsvector('english', COALESCE("processor", '')));

-- ========================================
-- EVENT ENTITY INDEXES
-- ========================================

-- Event type and format indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_type" ON "public"."entity_details_event"("event_type");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_format" ON "public"."entity_details_event"("event_format");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_online" ON "public"."entity_details_event"("is_online");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_registration" ON "public"."entity_details_event"("registration_required");

-- Event date indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_start_date" ON "public"."entity_details_event"("start_date") WHERE "start_date" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_end_date" ON "public"."entity_details_event"("end_date") WHERE "end_date" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_date_range" ON "public"."entity_details_event"("start_date", "end_date");

-- Event array field indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_speakers" ON "public"."entity_details_event" USING gin("key_speakers");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_target_audience" ON "public"."entity_details_event" USING gin("target_audience");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_topics" ON "public"."entity_details_event" USING gin("topics");

-- ========================================
-- SOFTWARE ENTITY INDEXES
-- ========================================

-- Software language and platform indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_languages" ON "public"."entity_details_software" USING gin("programming_languages");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_platforms" ON "public"."entity_details_software" USING gin("platform_compatibility");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_license" ON "public"."entity_details_software"("license_type");

-- Software feature indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_repository" ON "public"."entity_details_software"("repository_url") WHERE "repository_url" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_release_date" ON "public"."entity_details_software"("release_date") WHERE "release_date" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_software_open_source" ON "public"."entity_details_software"("open_source");

-- ========================================
-- RESEARCH PAPER ENTITY INDEXES
-- ========================================

-- Research paper field indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_areas" ON "public"."entity_details_research_paper" USING gin("research_areas");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_venues" ON "public"."entity_details_research_paper" USING gin("publication_venues");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_keywords" ON "public"."entity_details_research_paper" USING gin("keywords");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_authors" ON "public"."entity_details_research_paper" USING gin("authors");

-- Research paper date and citation indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_publication_date" ON "public"."entity_details_research_paper"("publication_date") WHERE "publication_date" IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_citation_count" ON "public"."entity_details_research_paper"("citation_count") WHERE "citation_count" IS NOT NULL;

-- Research paper text search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_research_abstract_search" ON "public"."entity_details_research_paper" USING gin(to_tsvector('english', COALESCE("abstract", '')));

-- ========================================
-- AGENCY ENTITY INDEXES
-- ========================================

-- Agency service and industry indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_agency_services" ON "public"."entity_details_agency" USING gin("services_offered");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_agency_industry" ON "public"."entity_details_agency" USING gin("industry_focus");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_agency_portfolio" ON "public"."entity_details_agency"("has_portfolio");

-- ========================================
-- BOOK ENTITY INDEXES
-- ========================================

-- Book indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_book_author" ON "public"."entity_details_book"("author");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_book_isbn" ON "public"."entity_details_book"("isbn");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_book_format" ON "public"."entity_details_book"("format");
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_book_publication_date" ON "public"."entity_details_book"("publication_date") WHERE "publication_date" IS NOT NULL;

-- ========================================
-- COMPOSITE INDEXES FOR COMMON QUERIES
-- ========================================

-- Popular entity combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_type_status_rating" ON "public"."entities"("entity_type_id", "status", "avg_rating" DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_entities_created_type_status" ON "public"."entities"("created_at" DESC, "entity_type_id", "status");

-- Tool feature combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_tool_api_free_opensource" ON "public"."entity_details_tool"("api_access", "has_free_tier", "open_source") WHERE "api_access" = true OR "has_free_tier" = true OR "open_source" = true;

-- Job location and salary combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_job_remote_salary" ON "public"."entity_details_job"("is_remote", "salary_min", "salary_max");

-- Event online and date combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_event_online_dates" ON "public"."entity_details_event"("is_online", "start_date", "end_date");

-- ========================================
-- STATISTICS UPDATE
-- ========================================

-- Update table statistics for better query planning
ANALYZE "public"."entities";
ANALYZE "public"."entity_details_tool";
ANALYZE "public"."entity_details_course";
ANALYZE "public"."entity_details_job";
ANALYZE "public"."entity_details_hardware";
ANALYZE "public"."entity_details_event";
ANALYZE "public"."entity_details_software";
ANALYZE "public"."entity_details_research_paper";
ANALYZE "public"."entity_details_agency";
ANALYZE "public"."entity_details_book";
