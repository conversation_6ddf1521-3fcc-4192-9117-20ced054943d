import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('E2E Filter Validation (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('Tool Filters', () => {
    it('should filter by API access', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ has_api: 'true', limit: 5 })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('data');
          expect(res.body).toHaveProperty('total');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('should filter by technical levels', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ technical_levels: ['BEGINNER', 'INTERMEDIATE'], limit: 5 })
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toBeDefined();
        });
    });

    it('should filter by multiple tool features', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_api: 'true',
          has_free_tier: 'true',
          open_source: 'true',
          mobile_support: 'true',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by frameworks and libraries', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          frameworks: ['TensorFlow', 'PyTorch'],
          libraries: ['OpenAI', 'Anthropic'],
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Course Filters', () => {
    it('should filter by skill levels and certificate', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          skill_levels: ['BEGINNER', 'INTERMEDIATE'],
          certificate_available: 'true',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by instructor and duration', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          instructor_name: 'Dr. Smith',
          duration_text: '10 hours',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by enrollment range', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          enrollment_min: 100,
          enrollment_max: 10000,
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Job Filters', () => {
    it('should filter by employment and experience types', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          employment_types: ['FULL_TIME', 'PART_TIME'],
          experience_levels: ['MID', 'SENIOR'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by location types', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          location_types: ['Remote', 'Hybrid'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by salary range', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          salary_min: 80,
          salary_max: 150,
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by company and job description', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          company_name: 'Google',
          job_description: 'machine learning',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Hardware Filters (Enhanced)', () => {
    it('should filter by hardware types', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          hardware_types: ['GPU', 'CPU', 'TPU'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by manufacturers', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          manufacturers: ['NVIDIA', 'Intel', 'AMD'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by release date range', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          release_date_from: '2023-01-01',
          release_date_to: '2024-12-31',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by price range', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          price_min: 500,
          price_max: 2000,
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by specifications and datasheet', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          specifications_search: 'GDDR6',
          has_datasheet: 'true',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by memory and processor specs', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          memory_search: '16GB',
          processor_search: 'Intel i9',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Event Filters (Enhanced)', () => {
    it('should filter by event types', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          event_types: ['Conference', 'Workshop', 'Webinar'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by date ranges', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          start_date_from: '2024-01-01',
          start_date_to: '2024-12-31',
          end_date_from: '2024-01-01',
          end_date_to: '2024-12-31',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by online and registration status', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          is_online: 'true',
          registration_required: 'true',
          has_registration_url: 'true',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by location and speakers', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          location: 'San Francisco',
          speakers_search: 'Elon Musk',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Software Filters (Enhanced)', () => {
    it('should filter by programming languages', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          programming_languages: ['Python', 'JavaScript', 'Java'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by platform compatibility', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          platform_compatibility: ['Windows', 'macOS', 'Linux'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by license types', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          license_types: ['MIT', 'Apache 2.0', 'GPL'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by repository and version', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_repository: 'true',
          current_version: '2.0',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Research Paper Filters (Enhanced)', () => {
    it('should filter by research areas', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          research_areas: ['Machine Learning', 'NLP', 'Computer Vision'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by authors and publication date', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          authors_search: 'Geoffrey Hinton',
          publication_date_from: '2020-01-01',
          publication_date_to: '2024-12-31',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Agency Filters', () => {
    it('should filter by services and industry focus', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          services_offered: ['AI Strategy', 'Machine Learning'],
          industry_focus: ['Healthcare', 'Finance'],
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by portfolio availability', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_portfolio: 'true',
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Book Filters', () => {
    it('should filter by author and ISBN', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          author_name: 'Andrew Ng',
          isbn: '978-**********',
          limit: 5 
        })
        .expect(200);
    });

    it('should filter by formats', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          formats: ['eBook', 'Audiobook', 'Hardcover'],
          limit: 5 
        })
        .expect(200);
    });
  });

  describe('Cross-Entity Filtering', () => {
    it('should combine tool and course filters', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_api: 'true',
          technical_levels: 'BEGINNER',
          certificate_available: 'true',
          skill_levels: 'INTERMEDIATE',
          limit: 10 
        })
        .expect(200);
    });

    it('should combine job and event filters', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          employment_types: 'FULL_TIME',
          location_types: 'Remote',
          is_online: 'true',
          event_types: 'Conference',
          limit: 10 
        })
        .expect(200);
    });

    it('should combine hardware and software filters', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          hardware_types: 'GPU',
          manufacturers: 'NVIDIA',
          programming_languages: 'Python',
          has_repository: 'true',
          limit: 10 
        })
        .expect(200);
    });

    it('should handle complex multi-entity filtering', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_api: 'true',
          technical_levels: 'BEGINNER',
          employment_types: 'FULL_TIME',
          hardware_types: 'GPU',
          research_areas: 'Machine Learning',
          certificate_available: 'true',
          limit: 20 
        })
        .expect(200);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty filters gracefully', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ limit: 5 })
        .expect(200);
    });

    it('should handle invalid boolean values', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ has_api: 'invalid', limit: 5 })
        .expect(400);
    });

    it('should handle invalid date formats', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ start_date_from: 'invalid-date', limit: 5 })
        .expect(400);
    });

    it('should handle invalid number values', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ salary_min: 'not-a-number', limit: 5 })
        .expect(400);
    });

    it('should handle large limit values', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ limit: 1000 })
        .expect(400);
    });

    it('should handle negative page numbers', () => {
      return request(app.getHttpServer())
        .get('/entities')
        .query({ page: -1, limit: 5 })
        .expect(400);
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .get('/entities')
        .query({ 
          has_api: 'true',
          technical_levels: 'BEGINNER',
          certificate_available: 'true',
          limit: 20 
        })
        .expect(200);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should respond within 5 seconds
    });

    it('should handle concurrent requests', async () => {
      const requests = Array.from({ length: 10 }, () =>
        request(app.getHttpServer())
          .get('/entities')
          .query({ has_api: 'true', limit: 5 })
          .expect(200)
      );

      await Promise.all(requests);
    });
  });
});
