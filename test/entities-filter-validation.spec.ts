import { Test, TestingModule } from '@nestjs/testing';
import { EntitiesService } from '../src/entities/entities.service';
import { PrismaService } from '../src/prisma/prisma.service';

describe('EntitiesService - Enhanced Filtering', () => {
  let service: EntitiesService;
  let prisma: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntitiesService,
        {
          provide: PrismaService,
          useValue: {
            entity: {
              findMany: jest.fn(),
              count: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<EntitiesService>(EntitiesService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  describe('Hardware Filters', () => {
    it('should filter by hardware types', async () => {
      const mockResult = { data: [], total: 0, page: 1, limit: 20, totalPages: 0 };
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      const result = await service.findAll({
        hardware_types: ['GPU', 'CPU'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsHardware: {
                  hardwareType: { in: ['GPU', 'CPU'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by manufacturers', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        manufacturers: ['NVIDIA', 'Intel'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsHardware: {
                  manufacturer: { in: ['NVIDIA', 'Intel'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by release date range', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        release_date_from: '2023-01-01',
        release_date_to: '2024-12-31',
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsHardware: {
                  releaseDate: {
                    gte: new Date('2023-01-01'),
                    lte: new Date('2024-12-31')
                  }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by datasheet availability', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        has_datasheet: true,
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsHardware: {
                  datasheetUrl: { not: null }
                }
              })
            ])
          })
        })
      );
    });
  });

  describe('Job Filters', () => {
    it('should filter by employment types', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        employment_types: ['FULL_TIME', 'PART_TIME'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsJob: {
                  employmentTypes: { hasSome: ['FULL_TIME', 'PART_TIME'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by experience levels', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        experience_levels: ['MID', 'SENIOR'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsJob: {
                  experienceLevel: { in: ['MID', 'SENIOR'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by job description', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        job_description: 'machine learning',
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsJob: {
                  jobDescription: { contains: 'machine learning', mode: 'insensitive' }
                }
              })
            ])
          })
        })
      );
    });
  });

  describe('Event Filters', () => {
    it('should filter by registration required', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        registration_required: true,
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsEvent: {
                  registrationRequired: true
                }
              })
            ])
          })
        })
      );
    });
  });

  describe('Software Filters', () => {
    it('should filter by programming languages', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        programming_languages: ['Python', 'JavaScript'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsSoftware: {
                  programmingLanguages: { hasSome: ['Python', 'JavaScript'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by repository availability', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        has_repository: true,
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsSoftware: {
                  repositoryUrl: { not: null }
                }
              })
            ])
          })
        })
      );
    });
  });

  describe('Research Paper Filters', () => {
    it('should filter by research areas', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        research_areas: ['Machine Learning', 'NLP'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsResearchPaper: {
                  researchAreas: { hasSome: ['Machine Learning', 'NLP'] }
                }
              })
            ])
          })
        })
      );
    });

    it('should filter by publication date range', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        publication_date_from: '2020-01-01',
        publication_date_to: '2024-12-31',
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsResearchPaper: {
                  publicationDate: {
                    gte: new Date('2020-01-01'),
                    lte: new Date('2024-12-31')
                  }
                }
              })
            ])
          })
        })
      );
    });
  });

  describe('Cross-Entity Filtering', () => {
    it('should combine filters across multiple entity types', async () => {
      jest.spyOn(prisma.entity, 'findMany').mockResolvedValue([]);
      jest.spyOn(prisma.entity, 'count').mockResolvedValue(0);

      await service.findAll({
        has_api: true,
        hardware_types: ['GPU'],
        employment_types: ['FULL_TIME'],
        research_areas: ['Machine Learning'],
        page: 1,
        limit: 20,
      });

      expect(prisma.entity.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                entityDetailsTool: { apiAccess: true }
              }),
              expect.objectContaining({
                entityDetailsHardware: { hardwareType: { in: ['GPU'] } }
              }),
              expect.objectContaining({
                entityDetailsJob: { employmentTypes: { hasSome: ['FULL_TIME'] } }
              }),
              expect.objectContaining({
                entityDetailsResearchPaper: { researchAreas: { hasSome: ['Machine Learning'] } }
              })
            ])
          })
        })
      );
    });
  });
});
