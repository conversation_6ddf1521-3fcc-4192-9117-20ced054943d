-- Manual Upvote Migration Script
-- Run this in Supabase SQL Editor if Prisma migration fails
-- This script is idempotent and safe to run multiple times

-- 1. Create user_upvotes table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS "public"."user_upvotes" (
    "user_id" UUID NOT NULL,
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "user_upvotes_pkey" PRIMARY KEY ("user_id","entity_id")
);

-- 2. Add foreign key constraints (if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_upvotes_user_id_fkey'
    ) THEN
        ALTER TABLE "public"."user_upvotes" 
        ADD CONSTRAINT "user_upvotes_user_id_fkey" 
        FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") 
        ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_upvotes_entity_id_fkey'
    ) THEN
        ALTER TABLE "public"."user_upvotes" 
        ADD CONSTRAINT "user_upvotes_entity_id_fkey" 
        FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") 
        ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

-- 3. Add upvote_count column to entities table (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'entities' 
        AND column_name = 'upvote_count'
    ) THEN
        ALTER TABLE "public"."entities" 
        ADD COLUMN "upvote_count" INTEGER NOT NULL DEFAULT 0;
    END IF;
END $$;

-- 4. Create the trigger function (replace if exists)
CREATE OR REPLACE FUNCTION public.update_entity_upvote_count()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'DELETE') THEN
    UPDATE public.entities
    SET upvote_count = upvote_count - 1
    WHERE id = OLD.entity_id;
    RETURN OLD;
  ELSIF (TG_OP = 'INSERT') THEN
    UPDATE public.entities
    SET upvote_count = upvote_count + 1
    WHERE id = NEW.entity_id;
    RETURN NEW;
  END IF;
  RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create the trigger (drop and recreate to ensure it's correct)
DROP TRIGGER IF EXISTS trigger_update_entity_upvote_count ON public.user_upvotes;
CREATE TRIGGER trigger_update_entity_upvote_count
AFTER INSERT OR DELETE ON public.user_upvotes
FOR EACH ROW EXECUTE FUNCTION public.update_entity_upvote_count();

-- 6. Initialize upvote_count for existing entities (if needed)
UPDATE public.entities 
SET upvote_count = (
    SELECT COUNT(*) 
    FROM public.user_upvotes 
    WHERE entity_id = entities.id
)
WHERE upvote_count = 0;

-- 7. Verify the setup
SELECT 
    'user_upvotes table' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'user_upvotes'
    ) THEN 'EXISTS' ELSE 'MISSING' END as status

UNION ALL

SELECT 
    'upvote_count column' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'entities' AND column_name = 'upvote_count'
    ) THEN 'EXISTS' ELSE 'MISSING' END as status

UNION ALL

SELECT 
    'trigger function' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_schema = 'public' AND routine_name = 'update_entity_upvote_count'
    ) THEN 'EXISTS' ELSE 'MISSING' END as status

UNION ALL

SELECT 
    'trigger' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_schema = 'public' AND trigger_name = 'trigger_update_entity_upvote_count'
    ) THEN 'EXISTS' ELSE 'MISSING' END as status;

-- Success message
SELECT 'Upvote migration completed successfully!' as message;
