"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitiesController = exports.VectorSearchDto = void 0;
const common_1 = require("@nestjs/common");
const entities_service_1 = require("./entities.service");
const create_entity_dto_1 = require("./dto/create-entity.dto");
const update_entity_dto_1 = require("./dto/update-entity.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const admin_guard_1 = require("../auth/guards/admin.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const list_entities_dto_1 = require("./dto/list-entities.dto");
const entity_response_dto_1 = require("./dto/entity-response.dto");
const paginated_entity_response_dto_1 = require("./dto/paginated-entity-response.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const entity_list_item_response_dto_1 = require("./dto/entity-list-item-response.dto");
class VectorSearchDto {
    constructor() {
        this.limit = 10;
    }
}
exports.VectorSearchDto = VectorSearchDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The search query string for vector similarity search.',
        example: 'AI tools for video generation',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VectorSearchDto.prototype, "query", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The maximum number of results to return.',
        example: 10,
        required: false,
        default: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Object)
], VectorSearchDto.prototype, "limit", void 0);
let EntitiesController = class EntitiesController {
    constructor(entitiesService) {
        this.entitiesService = entitiesService;
    }
    mapUserToMinimalDto(user) {
        if (!user)
            return null;
        const minimalDto = {
            id: user.authUserId,
            email: user.email,
            created_at: user.createdAt.toISOString(),
            last_sign_in_at: user.lastLogin ? user.lastLogin.toISOString() : null,
            user_metadata: {
                username: user.username,
                display_name: user.displayName,
                profile_picture_url: user.profilePictureUrl,
                internal_user_id: user.id
            },
        };
        return minimalDto;
    }
    mapEntityTypeToDto(entityType) {
        if (!entityType)
            return null;
        return {
            id: entityType.id,
            name: entityType.name,
            slug: entityType.slug,
            description: entityType.description,
            iconUrl: entityType.iconUrl,
            createdAt: entityType.createdAt,
            updatedAt: entityType.updatedAt,
        };
    }
    mapCategoryToDto(category) {
        if (!category)
            return null;
        return {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            iconUrl: category.iconUrl,
            parentCategoryId: category.parentId,
            createdAt: category.createdAt,
            updatedAt: category.updatedAt,
        };
    }
    mapTagToDto(tag) {
        if (!tag)
            return null;
        return {
            id: tag.id,
            name: tag.name,
            slug: tag.slug,
        };
    }
    mapFeatureToDto(feature) {
        if (!feature)
            return null;
        return {
            id: feature.id,
            name: feature.name,
            slug: feature.slug,
            description: feature.description,
            iconUrl: feature.iconUrl,
            createdAt: feature.createdAt,
            updatedAt: feature.updatedAt,
        };
    }
    mapToolDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            programmingLanguages: details.programmingLanguages,
            frameworks: details.frameworks,
            libraries: details.libraries,
            integrations: details.integrations,
            keyFeatures: details.keyFeatures,
            useCases: details.useCases,
            targetAudience: details.targetAudience,
            learningCurve: details.learningCurve,
            deploymentOptions: details.deploymentOptions,
            supportedOs: details.supportedOs,
            mobileSupport: details.mobileSupport,
            apiAccess: details.apiAccess,
            customizationLevel: details.customizationLevel,
            trialAvailable: details.trialAvailable,
            demoAvailable: details.demoAvailable,
            openSource: details.openSource,
            supportChannels: details.supportChannels,
            hasFreeTier: details.hasFreeTier,
            pricingModel: details.pricingModel,
            priceRange: details.priceRange,
            pricingDetails: details.pricingDetails,
            pricingUrl: details.pricingUrl,
            supportEmail: details.supportEmail,
            hasLiveChat: details.hasLiveChat,
            communityUrl: details.communityUrl,
        };
    }
    mapCourseDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            instructorName: details.instructorName,
            durationText: details.durationText,
            skillLevel: details.skillLevel,
            prerequisites: details.prerequisites,
            syllabusUrl: details.syllabusUrl,
            enrollmentCount: details.enrollmentCount,
            certificateAvailable: details.certificateAvailable,
        };
    }
    mapDatasetDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            format: details.format,
            size: details.size,
            numRecords: details.numRecords,
            accessUrl: details.accessUrl,
            downloadUrl: details.downloadUrl,
            license: details.license,
            updateFrequency: details.updateFrequency,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapResearchPaperDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            authors: details.authors,
            journal: details.journal,
            publicationDate: details.publicationDate,
            doi: details.doi,
            pdfUrl: details.pdfUrl,
            codeRepoUrl: details.codeRepoUrl,
            abstract: details.abstract,
            citations: details.citations,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapSoftwareDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            keyFeatures: details.keyFeatures,
            useCases: details.useCases,
            integrations: details.integrations,
            targetAudience: details.targetAudience,
            deploymentOptions: details.deploymentOptions,
            supportedOs: details.supportedOs,
            mobileSupport: details.mobileSupport,
            apiAccess: details.apiAccess,
            hasFreeTier: details.hasFreeTier,
            pricingModel: details.pricingModel,
            priceRange: details.priceRange,
            pricingDetails: details.pricingDetails,
            pricingUrl: details.pricingUrl,
            supportChannels: details.supportChannels,
            supportEmail: details.supportEmail,
            hasLiveChat: details.hasLiveChat,
            communityUrl: details.communityUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapModelDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            architecture: details.architecture,
            contextLength: details.contextLength,
            developer: details.developer,
            trainingData: details.trainingData,
            parameters: details.parameters,
            useCases: details.useCases,
            accessUrl: details.accessUrl,
            license: details.license,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapProjectReferenceDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            problemStatement: details.problemStatement,
            solutionOverview: details.solutionOverview,
            projectGoals: details.projectGoals,
            keyFeatures: details.keyFeatures,
            technologiesUsed: details.technologiesUsed,
            liveUrl: details.liveUrl,
            repoUrl: details.repoUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapServiceProviderDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            servicesOffered: details.servicesOffered,
            pricingInfo: details.pricingInfo,
            contactPerson: details.contactPerson,
            contactEmail: details.contactEmail,
            portfolioUrl: details.portfolioUrl,
            location: details.location,
            languagesSpoken: details.languagesSpoken,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapInvestorDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            investmentStage: details.investmentStage,
            portfolioSize: details.portfolioSize,
            checkSize: details.checkSize,
            focusAreas: details.focusAreas,
            notableInvestments: details.notableInvestments,
            contactPerson: details.contactPerson,
            contactEmail: details.contactEmail,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapCommunityDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            platform: details.platform,
            memberCount: details.memberCount,
            focusTopics: details.focusTopics,
            rulesUrl: details.rulesUrl,
            inviteUrl: details.inviteUrl,
            mainChannelUrl: details.mainChannelUrl,
        };
    }
    mapEventDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            format: details.format,
            startDate: details.startDate,
            endDate: details.endDate,
            location: details.location,
            registrationUrl: details.registrationUrl,
            scheduleUrl: details.scheduleUrl,
            speakerInfo: details.speakerInfo,
            price: details.price,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapJobDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            companyName: details.companyName,
            location: details.location,
            salaryRange: details.salaryRange,
            jobType: details.jobType,
            experienceLevel: details.experienceLevel,
            responsibilities: details.responsibilities,
            qualifications: details.qualifications,
            applyUrl: details.applyUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapGrantDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            fundingAmount: details.fundingAmount,
            fundingType: details.fundingType,
            applicationUrl: details.applicationUrl,
            deadline: details.deadline,
            eligibilityCriteria: details.eligibilityCriteria,
            regionFocus: details.regionFocus,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapBountyDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            rewardAmount: details.rewardAmount,
            rewardCurrency: details.rewardCurrency,
            status: details.status,
            deadline: details.deadline,
            requirements: details.requirements,
            platform: details.platform,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapHardwareDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            processor: details.processor,
            memory: details.memory,
            storage: details.storage,
            gpu: details.gpu,
            ports: details.ports,
            powerConsumption: details.powerConsumption,
            dimensions: details.dimensions,
            weight: details.weight,
            releaseDate: details.releaseDate,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapNewsDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            author: details.author,
            source: details.source,
            articleUrl: details.articleUrl,
            publishedDate: details.publishedDate,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapBookDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            author: details.author,
            publisher: details.publisher,
            publicationDate: details.publicationDate,
            isbn: details.isbn,
            pageCount: details.pageCount,
            format: details.format,
            language: details.language,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapPodcastDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            host: details.host,
            topics: details.topics,
            platformLinks: details.platformLinks,
            avgEpisodeLength: details.avgEpisodeLength,
            frequency: details.frequency,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapNewsletterDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            frequency: details.frequency,
            mainTopics: details.mainTopics,
            archiveUrl: details.archiveUrl,
            subscribeUrl: details.subscribeUrl,
            authorName: details.authorName,
            subscriberCount: details.subscriberCount,
        };
    }
    mapPlatformDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            keyFeatures: details.keyFeatures,
            useCases: details.useCases,
            integrations: details.integrations,
            targetAudience: details.targetAudience,
            deploymentOptions: details.deploymentOptions,
            supportedOs: details.supportedOs,
            mobileSupport: details.mobileSupport,
            apiAccess: details.apiAccess,
            hasFreeTier: details.hasFreeTier,
            pricingModel: details.pricingModel,
            priceRange: details.priceRange,
            pricingDetails: details.pricingDetails,
            pricingUrl: details.pricingUrl,
            supportChannels: details.supportChannels,
            supportEmail: details.supportEmail,
            hasLiveChat: details.hasLiveChat,
            communityUrl: details.communityUrl,
            createdAt: details.createdAt,
            updatedAt: details.updatedAt,
        };
    }
    mapAgencyDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            servicesOffered: details.servicesOffered,
            industryFocus: details.industryFocus,
            targetClientSize: details.targetClientSize,
            targetAudience: details.targetAudience,
            locationSummary: details.locationSummary,
            portfolioUrl: details.portfolioUrl,
            pricingInfo: details.pricingInfo,
        };
    }
    mapContentCreatorDetailsToDto(details) {
        if (!details)
            return null;
        return {
            entityId: details.entityId,
            creatorName: details.creatorName,
            primaryPlatform: details.primaryPlatform,
            focusAreas: details.focusAreas,
            followerCount: details.followerCount,
            exampleContentUrl: details.exampleContentUrl,
        };
    }
    mapEntityToListItemResponseDto(entity) {
        const listItemDto = new entity_list_item_response_dto_1.EntityListItemResponseDto();
        listItemDto.id = entity.id;
        listItemDto.name = entity.name;
        listItemDto.slug = entity.slug;
        listItemDto.logoUrl = entity.logoUrl;
        listItemDto.shortDescription = entity.shortDescription;
        listItemDto.websiteUrl = entity.websiteUrl;
        listItemDto.entityType = {
            name: entity.entityType.name,
            slug: entity.entityType.slug,
        };
        listItemDto.avgRating = entity.avgRating;
        listItemDto.reviewCount = entity.reviewCount;
        listItemDto.saveCount = entity._count?.userSavedEntities ?? 0;
        if (entity.entityType?.slug === 'ai-tool' && entity.entityDetailsTool) {
            listItemDto.hasFreeTier = entity.entityDetailsTool.hasFreeTier;
        }
        return listItemDto;
    }
    mapEntityToResponseDto(entity) {
        const responseDto = new entity_response_dto_1.EntityResponseDto();
        responseDto.id = entity.id;
        responseDto.name = entity.name;
        responseDto.slug = entity.slug || entity.id;
        responseDto.websiteUrl = entity.websiteUrl;
        responseDto.entityType = this.mapEntityTypeToDto(entity.entityType);
        responseDto.shortDescription = entity.shortDescription;
        responseDto.description = entity.description;
        responseDto.logoUrl = entity.logoUrl;
        responseDto.documentationUrl = entity.documentationUrl;
        responseDto.contactUrl = entity.contactUrl;
        responseDto.privacyPolicyUrl = entity.privacyPolicyUrl;
        responseDto.foundedYear = entity.foundedYear;
        responseDto.status = entity.status;
        responseDto.socialLinks = entity.socialLinks;
        responseDto.submitter = this.mapUserToMinimalDto(entity.submitter);
        responseDto.legacyId = entity.legacyId;
        responseDto.reviewCount = entity.reviewCount;
        responseDto.avgRating = entity.avgRating;
        responseDto.createdAt = entity.createdAt;
        responseDto.updatedAt = entity.updatedAt;
        responseDto.metaTitle = entity.metaTitle;
        responseDto.metaDescription = entity.metaDescription;
        responseDto.scrapedReviewSentimentLabel = entity.scrapedReviewSentimentLabel;
        responseDto.scrapedReviewSentimentScore = entity.scrapedReviewSentimentScore;
        responseDto.scrapedReviewCount = entity.scrapedReviewCount;
        responseDto.employeeCountRange = entity.employeeCountRange;
        responseDto.fundingStage = entity.fundingStage;
        responseDto.locationSummary = entity.locationSummary;
        responseDto.refLink = entity.refLink;
        responseDto.affiliateStatus = entity.affiliateStatus;
        responseDto.saveCount = entity._count?.userSavedEntities ?? 0;
        responseDto.categories = entity.entityCategories?.map(ec => this.mapCategoryToDto(ec.category)).filter(Boolean) || [];
        responseDto.tags = entity.entityTags?.map(et => this.mapTagToDto(et.tag)).filter(Boolean) || [];
        responseDto.features = entity.entityFeatures?.map(ef => this.mapFeatureToDto(ef.feature)).filter(Boolean) || [];
        switch (entity.entityType?.slug) {
            case 'ai-tool':
                responseDto.details = this.mapToolDetailsToDto(entity.entityDetailsTool);
                break;
            case 'online-course':
                responseDto.details = this.mapCourseDetailsToDto(entity.entityDetailsCourse);
                break;
            case 'dataset':
                responseDto.details = this.mapDatasetDetailsToDto(entity.entityDetailsDataset);
                break;
            case 'research-paper':
                responseDto.details = this.mapResearchPaperDetailsToDto(entity.entityDetailsResearchPaper);
                break;
            case 'software':
                responseDto.details = this.mapSoftwareDetailsToDto(entity.entityDetailsSoftware);
                break;
            case 'ai-model':
            case 'model':
                responseDto.details = this.mapModelDetailsToDto(entity.entityDetailsModel);
                break;
            case 'project-reference':
                responseDto.details = this.mapProjectReferenceDetailsToDto(entity.entityDetailsProjectReference);
                break;
            case 'service-provider':
                responseDto.details = this.mapServiceProviderDetailsToDto(entity.entityDetailsServiceProvider);
                break;
            case 'investor':
                responseDto.details = this.mapInvestorDetailsToDto(entity.entityDetailsInvestor);
                break;
            case 'community':
                responseDto.details = this.mapCommunityDetailsToDto(entity.entityDetailsCommunity);
                break;
            case 'event':
                responseDto.details = this.mapEventDetailsToDto(entity.entityDetailsEvent);
                break;
            case 'job-listing':
            case 'job':
                responseDto.details = this.mapJobDetailsToDto(entity.entityDetailsJob);
                break;
            case 'grant':
                responseDto.details = this.mapGrantDetailsToDto(entity.entityDetailsGrant);
                break;
            case 'bounty':
                responseDto.details = this.mapBountyDetailsToDto(entity.entityDetailsBounty);
                break;
            case 'hardware':
                responseDto.details = this.mapHardwareDetailsToDto(entity.entityDetailsHardware);
                break;
            case 'news':
                responseDto.details = this.mapNewsDetailsToDto(entity.entityDetailsNews);
                break;
            case 'book':
                responseDto.details = this.mapBookDetailsToDto(entity.entityDetailsBook);
                break;
            case 'podcast':
                responseDto.details = this.mapPodcastDetailsToDto(entity.entityDetailsPodcast);
                break;
            case 'newsletter':
                responseDto.details = this.mapNewsletterDetailsToDto(entity.entityDetailsNewsletter);
                break;
            case 'platform':
                responseDto.details = this.mapPlatformDetailsToDto(entity.entityDetailsPlatform);
                break;
            case 'agency':
                responseDto.details = this.mapAgencyDetailsToDto(entity.entityDetailsAgency);
                break;
            case 'content-creator':
                responseDto.details = this.mapContentCreatorDetailsToDto(entity.entityDetailsContentCreator);
                break;
            default:
                responseDto.details = null;
        }
        return responseDto;
    }
    async create(createEntityDto, user) {
        const entity = await this.entitiesService.create(createEntityDto, user);
        const fullEntity = await this.entitiesService.findOne(entity.id);
        if (!fullEntity)
            throw new common_1.NotFoundException('Failed to retrieve created entity details.');
        return this.mapEntityToResponseDto(fullEntity);
    }
    async findAll(listEntitiesDto) {
        const paginatedResult = await this.entitiesService.findAll(listEntitiesDto);
        return {
            ...paginatedResult,
            data: paginatedResult.data.map(entity => this.mapEntityToListItemResponseDto(entity)),
        };
    }
    async vectorSearch(vectorSearchDto) {
        return this.entitiesService.vectorSearch(vectorSearchDto);
    }
    async findBySlug(slug) {
        const entity = await this.entitiesService.findBySlug(slug);
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with slug "${slug}" not found.`);
        }
        return this.mapEntityToResponseDto(entity);
    }
    async findOne(id) {
        const entity = await this.entitiesService.findOne(id);
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID ${id} not found.`);
        }
        return this.mapEntityToResponseDto(entity);
    }
    async update(id, updateEntityDto, user) {
        const entity = await this.entitiesService.update(id, updateEntityDto, user);
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID ${id} not found or update failed.`);
        }
        const fullEntity = await this.entitiesService.findOne(entity.id);
        if (!fullEntity)
            throw new common_1.NotFoundException('Failed to retrieve updated entity details.');
        return this.mapEntityToResponseDto(fullEntity);
    }
    async remove(id, user) {
        await this.entitiesService.remove(id, user);
    }
};
exports.EntitiesController = EntitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new entity (submission for review)',
        description: `
    **Enhanced Entity Creation API**

    Submit new AI entities for review with comprehensive details across all supported entity types.

    **Supported Entity Types:**
    - 🔧 **Tools**: AI tools with technical levels, frameworks, API access, pricing models
    - 📚 **Courses**: Educational content with skill levels, certificates, instructors, duration
    - 💼 **Jobs**: AI job postings with employment types, experience levels, salary ranges, location types
    - 🖥️ **Hardware**: AI hardware with types, manufacturers, specifications, release dates
    - 📅 **Events**: AI events with types, dates, formats, speakers, registration details
    - 💻 **Software**: AI software with languages, platforms, licenses, repositories
    - 📄 **Research Papers**: Academic papers with research areas, authors, publication venues, keywords
    - 🏢 **Agencies**: AI agencies with services, industry focus, portfolios
    - 📖 **Books**: AI books with authors, ISBN, formats, publication details
    - 📰 **Newsletters**: AI newsletters with frequency, focus areas, target audience
    - 🎙️ **Podcasts**: AI podcasts with hosts, topics, platforms, frequency
    - 👥 **Communities**: AI communities with types, focus areas, member counts
    - 💰 **Grants**: AI funding opportunities with amounts, deadlines, eligibility

    **Enhanced Features:**
    - ✅ **Type-Specific Details**: Rich detail schemas for each entity type
    - ✅ **Enum Validation**: Proper validation for employment types, hardware types, etc.
    - ✅ **Array Support**: Multiple values for frameworks, languages, platforms, etc.
    - ✅ **Comprehensive Metadata**: SEO fields, social links, funding stages, employee counts
    - ✅ **Automatic Review Process**: All submissions go through moderation

    **Example Request Bodies:**

    **Tool Submission:**
    \`\`\`json
    {
      "name": "Awesome AI Tool",
      "website_url": "https://awesomeai.com",
      "entity_type_id": "tool-uuid",
      "description": "Revolutionary AI development platform",
      "tool_details": {
        "technical_level": "INTERMEDIATE",
        "has_api": true,
        "has_free_tier": true,
        "frameworks": ["TensorFlow", "PyTorch"],
        "pricing_model": "FREEMIUM"
      }
    }
    \`\`\`

    **Job Submission:**
    \`\`\`json
    {
      "name": "Senior AI Engineer",
      "website_url": "https://company.com/jobs/ai-engineer",
      "entity_type_id": "job-uuid",
      "job_details": {
        "company_name": "Tech Corp",
        "employment_types": ["FULL_TIME"],
        "experience_level": "SENIOR",
        "location_types": ["Remote", "Hybrid"],
        "salary_min": 120,
        "salary_max": 180,
        "job_description": "Lead AI initiatives..."
      }
    }
    \`\`\`

    **Hardware Submission:**
    \`\`\`json
    {
      "name": "RTX 4090 AI Accelerator",
      "website_url": "https://nvidia.com/rtx4090",
      "entity_type_id": "hardware-uuid",
      "hardware_details": {
        "hardware_type": "GPU",
        "manufacturer": "NVIDIA",
        "specifications": {
          "memory": "24GB GDDR6X",
          "cuda_cores": 16384,
          "tensor_performance": "165 TFLOPs"
        },
        "release_date": "2022-10-12"
      }
    }
    \`\`\`
    `
    }),
    (0, swagger_1.ApiBody)({ type: create_entity_dto_1.CreateEntityDto, description: 'Comprehensive entity data with type-specific details' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'The entity has been successfully submitted for review.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data. Please check the request body for errors.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to perform this action.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Conflict. An entity with some unique identifier (e.g., name or URL) might already exist, or a related resource was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_entity_dto_1.CreateEntityDto, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'List all entities with comprehensive filtering, pagination, and sorting',
        description: `
    **Enhanced Entity Filtering API**

    This endpoint supports comprehensive filtering across all entity types with 80+ filter parameters.

    **Key Features:**
    - ✅ Cross-entity filtering (combine filters from different entity types)
    - ✅ Flat parameter structure (no nested JSON required)
    - ✅ Array parameter support (multiple values per filter)
    - ✅ Full-text search capabilities
    - ✅ Date range filtering
    - ✅ Numeric range filtering
    - ✅ Boolean filters

    **Entity Types Supported:**
    - 🔧 **Tools**: API access, technical levels, frameworks, libraries, etc.
    - 📚 **Courses**: Skill levels, certificates, instructors, duration, etc.
    - 💼 **Jobs**: Employment types, experience levels, salary ranges, etc.
    - 🖥️ **Hardware**: Types, manufacturers, specifications, release dates, etc.
    - 📅 **Events**: Types, dates, online status, registration, speakers, etc.
    - 💻 **Software**: Languages, platforms, licenses, repositories, etc.
    - 📄 **Research Papers**: Areas, authors, publication dates, etc.
    - 🏢 **Agencies**: Services, industry focus, portfolios, etc.
    - 📖 **Books**: Authors, ISBN, formats, etc.

    **Example Usage:**
    \`\`\`
    GET /entities?has_api=true&technical_levels=BEGINNER&employment_types=FULL_TIME&hardware_types=GPU
    \`\`\`

    **Array Parameters:**
    Use multiple query parameters or comma-separated values:
    \`\`\`
    ?technical_levels=BEGINNER&technical_levels=INTERMEDIATE
    # OR
    ?technical_levels=BEGINNER,INTERMEDIATE
    \`\`\`
    `
    }),
    (0, swagger_1.ApiOkResponse)({ description: 'A paginated list of entities matching the criteria.', type: paginated_entity_response_dto_1.PaginatedEntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid query parameters. Please check the filter or pagination values.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [list_entities_dto_1.ListEntitiesDto]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)('vector-search'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Perform a semantic vector search for entities',
        description: 'Finds entities that are semantically similar to the provided query string by comparing vector embeddings.',
    }),
    (0, swagger_1.ApiBody)({ type: VectorSearchDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns a list of similar entities.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Validation failed on the request body.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [VectorSearchDto]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "vectorSearch", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a single entity by its slug' }),
    (0, swagger_1.ApiParam)({ name: 'slug', description: 'The URL-friendly slug of the entity.', type: 'string' }),
    (0, swagger_1.ApiOkResponse)({ description: 'The entity was found and returned.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified slug was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a single entity by its ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The UUID of the entity.', type: 'string' }),
    (0, swagger_1.ApiOkResponse)({ description: 'The entity was found and returned.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update an existing entity. Submitter can update PENDING/ACTIVE/REJECTED/INACTIVE entities (ACTIVE changes to PENDING). Admin can update any.' }),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, description: 'UUID of the entity to update', type: String, format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: update_entity_dto_1.UpdateEntityDto, description: 'Data to update the entity' }),
    (0, swagger_1.ApiOkResponse)({ description: 'The entity has been successfully updated.', type: entity_response_dto_1.EntityResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data or invalid UUID format.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. You do not have permission to update this entity or perform this status transition.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found, or a related resource (like a category/tag ID) is invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Conflict. An update would violate a unique constraint (e.g., name or URL).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_entity_dto_1.UpdateEntityDto, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Archive an entity by ID (Admin only). This performs a soft delete.' }),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, description: 'UUID of the entity to archive', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NO_CONTENT, description: 'The entity has been successfully archived.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid UUID format for entity ID.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Unauthorized. JWT token is missing or invalid.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.FORBIDDEN, description: 'Forbidden. Only administrators can archive entities.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Entity with the specified ID was not found.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error. Please try again later.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], EntitiesController.prototype, "remove", null);
exports.EntitiesController = EntitiesController = __decorate([
    (0, swagger_1.ApiTags)('Entities'),
    (0, common_1.Controller)('entities'),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService])
], EntitiesController);
//# sourceMappingURL=entities.controller.js.map