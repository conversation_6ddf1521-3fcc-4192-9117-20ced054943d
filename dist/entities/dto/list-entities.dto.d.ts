import { EntityStatus, <PERSON>risma, EmployeeCountRange, FundingStage, PricingModel, PriceRange, TechnicalLevel, LearningCurve, SkillLevel, EmploymentTypeEnum, HardwareTypeEnum } from '@generated-prisma';
export declare class ListEntitiesDto {
    page?: number;
    limit?: number;
    status?: EntityStatus;
    entityTypeIds?: string[];
    categoryIds?: string[];
    tagIds?: string[];
    featureIds?: string[];
    searchTerm?: string;
    sortBy?: string;
    sortOrder?: Prisma.SortOrder;
    submitterId?: string;
    createdAtFrom?: Date;
    createdAtTo?: Date;
    hasFreeTier?: boolean;
    employeeCountRanges?: EmployeeCountRange[];
    fundingStages?: FundingStage[];
    locationSearch?: string;
    apiAccess?: boolean;
    pricingModels?: PricingModel[];
    priceRanges?: PriceRange[];
    integrations?: string[];
    platforms?: string[];
    targetAudience?: string[];
    rating_min?: number;
    rating_max?: number;
    review_count_min?: number;
    review_count_max?: number;
    affiliate_status?: string;
    has_affiliate_link?: boolean;
    technical_levels?: TechnicalLevel[];
    learning_curves?: LearningCurve[];
    has_api?: boolean;
    has_free_tier?: boolean;
    open_source?: boolean;
    mobile_support?: boolean;
    demo_available?: boolean;
    frameworks?: string[];
    libraries?: string[];
    key_features_search?: string;
    use_cases_search?: string;
    target_audience_search?: string;
    deployment_options?: string[];
    support_channels?: string[];
    has_live_chat?: boolean;
    customization_level?: string;
    pricing_details_search?: string;
    skill_levels?: SkillLevel[];
    certificate_available?: boolean;
    instructor_name?: string;
    duration_text?: string;
    enrollment_min?: number;
    enrollment_max?: number;
    prerequisites?: string;
    has_syllabus?: boolean;
    employment_types?: EmploymentTypeEnum[];
    experience_levels?: string[];
    location_types?: string[];
    company_name?: string;
    job_title?: string;
    salary_min?: number;
    salary_max?: number;
    job_description?: string;
    has_application_url?: boolean;
    hardware_types?: HardwareTypeEnum[];
    manufacturers?: string[];
    release_date_from?: string;
    release_date_to?: string;
    price_range?: string;
    price_min?: number;
    price_max?: number;
    specifications_search?: string;
    has_datasheet?: boolean;
    memory_search?: string;
    processor_search?: string;
    event_types?: string[];
    start_date_from?: string;
    start_date_to?: string;
    end_date_from?: string;
    end_date_to?: string;
    is_online?: boolean;
    location?: string;
    price_text?: string;
    registration_required?: boolean;
    has_registration_url?: boolean;
    speakers_search?: string;
    services_offered?: string[];
    industry_focus?: string[];
    has_portfolio?: boolean;
    license_types?: string[];
    programming_languages?: string[];
    platform_compatibility?: string[];
    has_repository?: boolean;
    current_version?: string;
    research_areas?: string[];
    authors_search?: string;
    publication_date_from?: string;
    publication_date_to?: string;
    author_name?: string;
    isbn?: string;
    formats?: string[];
}
