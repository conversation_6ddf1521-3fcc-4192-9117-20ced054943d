import { EmploymentTypeEnum, ExperienceLevelEnum, LocationTypeEnum } from 'generated/prisma';
export declare class CreateJobDetailsDto {
    job_title?: string;
    company_name?: string;
    employment_types?: EmploymentTypeEnum[];
    experience_level?: ExperienceLevelEnum;
    location_types?: LocationTypeEnum[];
    salary_min?: number;
    salary_max?: number;
    application_url?: string;
    job_description?: string;
    is_remote?: boolean;
    location?: string;
    job_type?: string;
    key_responsibilities?: string[];
    required_skills?: string[];
    benefits?: string[];
    remote_policy?: string;
    visa_sponsorship?: boolean;
}
