import { EventFormatEnum } from 'generated/prisma';
export declare class CreateEventDetailsDto {
    event_type?: string;
    start_date?: Date;
    end_date?: Date;
    location?: string;
    is_online?: boolean;
    event_format?: EventFormatEnum;
    registration_required?: boolean;
    registration_url?: string;
    capacity?: number;
    organizer?: string;
    key_speakers?: string[];
    target_audience?: string[];
    topics?: string[];
    price?: string;
}
